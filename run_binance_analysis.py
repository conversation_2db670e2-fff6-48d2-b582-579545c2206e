from platforms.binance.binance_analyzer import BinanceMarketAnalyzer

if __name__ == "__main__":
    # API配置
    API_KEY = "Mf1zIJlnuy2DOTY7UI1Jo4ER6MAYAcj9IbpuCrOWkKLpegzbazRaTQr8F39Pps4y"
    API_SECRET = "5wB8Sk4tSYFYDtG2OpOJuPf5GwLX54jOR0TsLdZ3p5ClFjLnc93enr1tYNqkzMnj"
    
    # 创建分析器实例并运行
    analyzer = BinanceMarketAnalyzer(API_KEY, API_SECRET)
    analyzer.analyze_market(
        limit=10,
        min_volume=1000000,
        market_cap_tiers=['Large']
    ) 