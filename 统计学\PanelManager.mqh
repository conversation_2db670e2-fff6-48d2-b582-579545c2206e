//+------------------------------------------------------------------+
//|                                                 PanelManager.mqh |
//|                                  动态概率统计分析系统UI面板管理器 |
//|                                                                  |
//+------------------------------------------------------------------+

#ifndef PANEL_MANAGER_MQH
#define PANEL_MANAGER_MQH

#include "Defines.mqh"
#include <ChartObjects\ChartObjectsLabels.mqh>
#include <ChartObjects\ChartObjectsShapes.mqh>

//+------------------------------------------------------------------+
//| 面板配置常量                                                     |
//+------------------------------------------------------------------+
#define PANEL_WIDTH     320
#define PANEL_HEIGHT    600
#define LINE_HEIGHT     16
#define LEFT_MARGIN     8
#define TOP_MARGIN      8

//+------------------------------------------------------------------+
//| 获取对象前缀                                                     |
//+------------------------------------------------------------------+
string GetObjectPrefix(const long chart_id)
  {
   return "ProbStats_" + (string)chart_id + "_";
  }

//+------------------------------------------------------------------+
//| 创建面板                                                         |
//+------------------------------------------------------------------+
void Panel_Create(const long chart_id, const int corner, const int x_offset, const int y_offset)
  {
   string prefix = GetObjectPrefix(chart_id);
   
   // 创建主面板背景
   CChartObjectRectLabel *panel = new CChartObjectRectLabel();
   if(panel.Create(chart_id, prefix + "MainPanel", 0, x_offset, y_offset, PANEL_WIDTH, PANEL_HEIGHT))
     {
      panel.BackColor(clrBlack);
      panel.Color(clrWhite);
      panel.Corner((ENUM_BASE_CORNER)corner);
      panel.Style(STYLE_SOLID);
      panel.Width(1);
     }
   delete panel;
   
   int current_y = TOP_MARGIN;
   
   // 标题
   CChartObjectLabel *title = new CChartObjectLabel();
   if(title.Create(chart_id, prefix + "Title", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      title.Description("动态概率统计分析器 v2.0");
      title.FontSize(12);
      title.Color(clrYellow);
      title.Corner((ENUM_BASE_CORNER)corner);
     }
   delete title;
   current_y += LINE_HEIGHT + 5;
   
   // 分隔线
   CChartObjectLabel *separator1 = new CChartObjectLabel();
   if(separator1.Create(chart_id, prefix + "Separator1", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      separator1.Description("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      title.FontSize(8);
      separator1.Color(clrGray);
      separator1.Corner((ENUM_BASE_CORNER)corner);
     }
   delete separator1;
   current_y += LINE_HEIGHT;
   
   // 当前时间信息
   CChartObjectLabel *time_label = new CChartObjectLabel();
   if(time_label.Create(chart_id, prefix + "TimeLabel", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      time_label.Description("当前北京时间: --:--:--");
      time_label.FontSize(9);
      time_label.Color(clrWhite);
      time_label.Corner((ENUM_BASE_CORNER)corner);
     }
   delete time_label;
   current_y += LINE_HEIGHT;
   
   // 当前交易时段
   CChartObjectLabel *session_label = new CChartObjectLabel();
   if(session_label.Create(chart_id, prefix + "SessionLabel", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      session_label.Description("当前交易时段: 检测中...");
      session_label.FontSize(9);
      session_label.Color(clrLightBlue);
      session_label.Corner((ENUM_BASE_CORNER)corner);
     }
   delete session_label;
   current_y += LINE_HEIGHT + 5;
   
   // 分隔线
   CChartObjectLabel *separator2 = new CChartObjectLabel();
   if(separator2.Create(chart_id, prefix + "Separator2", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      separator2.Description("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      separator2.FontSize(8);
      separator2.Color(clrGray);
      separator2.Corner((ENUM_BASE_CORNER)corner);
     }
   delete separator2;
   current_y += LINE_HEIGHT;
   
   // 当前时段统计
   CChartObjectLabel *current_stats_title = new CChartObjectLabel();
   if(current_stats_title.Create(chart_id, prefix + "CurrentStatsTitle", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      current_stats_title.Description("【当前时段统计】");
      current_stats_title.FontSize(10);
      current_stats_title.Color(clrYellow);
      current_stats_title.Corner((ENUM_BASE_CORNER)corner);
     }
   delete current_stats_title;
   current_y += LINE_HEIGHT;
   
   // 当前时段胜率
   CChartObjectLabel *current_winrate = new CChartObjectLabel();
   if(current_winrate.Create(chart_id, prefix + "CurrentWinRate", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      current_winrate.Description("胜率: --%");
      current_winrate.FontSize(9);
      current_winrate.Color(clrWhite);
      current_winrate.Corner((ENUM_BASE_CORNER)corner);
     }
   delete current_winrate;
   current_y += LINE_HEIGHT;
   
   // 当前时段样本数
   CChartObjectLabel *current_samples = new CChartObjectLabel();
   if(current_samples.Create(chart_id, prefix + "CurrentSamples", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      current_samples.Description("样本数: --");
      current_samples.FontSize(9);
      current_samples.Color(clrWhite);
      current_samples.Corner((ENUM_BASE_CORNER)corner);
     }
   delete current_samples;
   current_y += LINE_HEIGHT;
   
   // 当前时段平均波幅
   CChartObjectLabel *current_volatility = new CChartObjectLabel();
   if(current_volatility.Create(chart_id, prefix + "CurrentVolatility", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      current_volatility.Description("平均波幅: -- pips");
      current_volatility.FontSize(9);
      current_volatility.Color(clrWhite);
      current_volatility.Corner((ENUM_BASE_CORNER)corner);
     }
   delete current_volatility;
   current_y += LINE_HEIGHT + 5;
   
   // 分隔线
   CChartObjectLabel *separator3 = new CChartObjectLabel();
   if(separator3.Create(chart_id, prefix + "Separator3", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      separator3.Description("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      separator3.FontSize(8);
      separator3.Color(clrGray);
      separator3.Corner((ENUM_BASE_CORNER)corner);
     }
   delete separator3;
   current_y += LINE_HEIGHT;
   
   // 历史统计概览
   CChartObjectLabel *history_title = new CChartObjectLabel();
   if(history_title.Create(chart_id, prefix + "HistoryTitle", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      history_title.Description("【历史统计概览】");
      history_title.FontSize(10);
      history_title.Color(clrYellow);
      history_title.Corner((ENUM_BASE_CORNER)corner);
     }
   delete history_title;
   current_y += LINE_HEIGHT;
   
   // 分析天数
   CChartObjectLabel *analysis_days = new CChartObjectLabel();
   if(analysis_days.Create(chart_id, prefix + "AnalysisDays", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      analysis_days.Description("分析天数: --");
      analysis_days.FontSize(9);
      analysis_days.Color(clrWhite);
      analysis_days.Corner((ENUM_BASE_CORNER)corner);
     }
   delete analysis_days;
   current_y += LINE_HEIGHT;
   
   // 总样本数
   CChartObjectLabel *total_samples = new CChartObjectLabel();
   if(total_samples.Create(chart_id, prefix + "TotalSamples", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      total_samples.Description("总样本数: --");
      total_samples.FontSize(9);
      total_samples.Color(clrWhite);
      total_samples.Corner((ENUM_BASE_CORNER)corner);
     }
   delete total_samples;
   current_y += LINE_HEIGHT;
   
   // 整体胜率
   CChartObjectLabel *overall_winrate = new CChartObjectLabel();
   if(overall_winrate.Create(chart_id, prefix + "OverallWinRate", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      overall_winrate.Description("整体胜率: --%");
      overall_winrate.FontSize(9);
      overall_winrate.Color(clrLightGreen);
      overall_winrate.Corner((ENUM_BASE_CORNER)corner);
     }
   delete overall_winrate;
   current_y += LINE_HEIGHT + 5;
   
   // 分隔线
   CChartObjectLabel *separator4 = new CChartObjectLabel();
   if(separator4.Create(chart_id, prefix + "Separator4", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      separator4.Description("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      separator4.FontSize(8);
      separator4.Color(clrGray);
      separator4.Corner((ENUM_BASE_CORNER)corner);
     }
   delete separator4;
   current_y += LINE_HEIGHT;
   
   // 分隔线
   CChartObjectLabel *separator5 = new CChartObjectLabel();
   if(separator5.Create(chart_id, prefix + "Separator5", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      separator5.Description("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      separator5.FontSize(8);
      separator5.Color(clrGray);
      separator5.Corner((ENUM_BASE_CORNER)corner);
     }
   delete separator5;
   current_y += LINE_HEIGHT;

   // 周期效应分析
   CChartObjectLabel *periodic_title = new CChartObjectLabel();
   if(periodic_title.Create(chart_id, prefix + "PeriodicTitle", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      periodic_title.Description("【周期效应分析】");
      periodic_title.FontSize(10);
      periodic_title.Color(clrYellow);
      periodic_title.Corner((ENUM_BASE_CORNER)corner);
     }
   delete periodic_title;
   current_y += LINE_HEIGHT;

   // 今日星期表现
   CChartObjectLabel *weekday_stats = new CChartObjectLabel();
   if(weekday_stats.Create(chart_id, prefix + "WeekdayStats", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      weekday_stats.Description("今日(--): 胜率--%");
      weekday_stats.FontSize(9);
      weekday_stats.Color(clrWhite);
      weekday_stats.Corner((ENUM_BASE_CORNER)corner);
     }
   delete weekday_stats;
   current_y += LINE_HEIGHT;

   // 本月表现
   CChartObjectLabel *month_stats = new CChartObjectLabel();
   if(month_stats.Create(chart_id, prefix + "MonthStats", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      month_stats.Description("本月(--): 胜率--%");
      month_stats.FontSize(9);
      month_stats.Color(clrWhite);
      month_stats.Corner((ENUM_BASE_CORNER)corner);
     }
   delete month_stats;
   current_y += LINE_HEIGHT + 5;

   // 分隔线
   CChartObjectLabel *separator6 = new CChartObjectLabel();
   if(separator6.Create(chart_id, prefix + "Separator6", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      separator6.Description("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      separator6.FontSize(8);
      separator6.Color(clrGray);
      separator6.Corner((ENUM_BASE_CORNER)corner);
     }
   delete separator6;
   current_y += LINE_HEIGHT;

   // 波动率分析
   CChartObjectLabel *volatility_title = new CChartObjectLabel();
   if(volatility_title.Create(chart_id, prefix + "VolatilityTitle", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      volatility_title.Description("【波动率分析】");
      volatility_title.FontSize(10);
      volatility_title.Color(clrYellow);
      volatility_title.Corner((ENUM_BASE_CORNER)corner);
     }
   delete volatility_title;
   current_y += LINE_HEIGHT;

   // 历史日均波幅
   CChartObjectLabel *avg_range = new CChartObjectLabel();
   if(avg_range.Create(chart_id, prefix + "AvgRange", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      avg_range.Description("日均波幅: -- pips");
      avg_range.FontSize(9);
      avg_range.Color(clrWhite);
      avg_range.Corner((ENUM_BASE_CORNER)corner);
     }
   delete avg_range;
   current_y += LINE_HEIGHT;

   // 今日已走波幅
   CChartObjectLabel *today_range = new CChartObjectLabel();
   if(today_range.Create(chart_id, prefix + "TodayRange", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      today_range.Description("今日已走: -- pips");
      today_range.FontSize(9);
      today_range.Color(clrWhite);
      today_range.Corner((ENUM_BASE_CORNER)corner);
     }
   delete today_range;
   current_y += LINE_HEIGHT;

   // 完成百分比
   CChartObjectLabel *completion = new CChartObjectLabel();
   if(completion.Create(chart_id, prefix + "Completion", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      completion.Description("完成度: --%");
      completion.FontSize(9);
      completion.Color(clrWhite);
      completion.Corner((ENUM_BASE_CORNER)corner);
     }
   delete completion;
   current_y += LINE_HEIGHT;

   // 预估剩余空间
   CChartObjectLabel *remaining = new CChartObjectLabel();
   if(remaining.Create(chart_id, prefix + "Remaining", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      remaining.Description("剩余空间: -- pips");
      remaining.FontSize(9);
      remaining.Color(clrWhite);
      remaining.Corner((ENUM_BASE_CORNER)corner);
     }
   delete remaining;
   current_y += LINE_HEIGHT + 5;

   // 分隔线
   CChartObjectLabel *separator7 = new CChartObjectLabel();
   if(separator7.Create(chart_id, prefix + "Separator7", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      separator7.Description("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      separator7.FontSize(8);
      separator7.Color(clrGray);
      separator7.Corner((ENUM_BASE_CORNER)corner);
     }
   delete separator7;
   current_y += LINE_HEIGHT;

   // 走势模式识别
   CChartObjectLabel *pattern_title = new CChartObjectLabel();
   if(pattern_title.Create(chart_id, prefix + "PatternTitle", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      pattern_title.Description("【走势模式识别】");
      pattern_title.FontSize(10);
      pattern_title.Color(clrYellow);
      pattern_title.Corner((ENUM_BASE_CORNER)corner);
     }
   delete pattern_title;
   current_y += LINE_HEIGHT;

   // 识别模式
   CChartObjectLabel *pattern_name = new CChartObjectLabel();
   if(pattern_name.Create(chart_id, prefix + "PatternName", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      pattern_name.Description("模式: 未知");
      pattern_name.FontSize(9);
      pattern_name.Color(clrWhite);
      pattern_name.Corner((ENUM_BASE_CORNER)corner);
     }
   delete pattern_name;
   current_y += LINE_HEIGHT;

   // 置信度
   CChartObjectLabel *pattern_confidence = new CChartObjectLabel();
   if(pattern_confidence.Create(chart_id, prefix + "PatternConfidence", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      pattern_confidence.Description("置信度: --%");
      pattern_confidence.FontSize(9);
      pattern_confidence.Color(clrWhite);
      pattern_confidence.Corner((ENUM_BASE_CORNER)corner);
     }
   delete pattern_confidence;
   current_y += LINE_HEIGHT + 5;

   // 分隔线
   CChartObjectLabel *separator8 = new CChartObjectLabel();
   if(separator8.Create(chart_id, prefix + "Separator8", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      separator8.Description("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      separator8.FontSize(8);
      separator8.Color(clrGray);
      separator8.Corner((ENUM_BASE_CORNER)corner);
     }
   delete separator8;
   current_y += LINE_HEIGHT;

   // 智能评分系统
   CChartObjectLabel *scoring_title = new CChartObjectLabel();
   if(scoring_title.Create(chart_id, prefix + "ScoringTitle", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      scoring_title.Description("【智能评分系统】");
      scoring_title.FontSize(10);
      scoring_title.Color(clrYellow);
      scoring_title.Corner((ENUM_BASE_CORNER)corner);
     }
   delete scoring_title;
   current_y += LINE_HEIGHT;

   // 综合评分
   CChartObjectLabel *final_score = new CChartObjectLabel();
   if(final_score.Create(chart_id, prefix + "FinalScore", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      final_score.Description("综合评分: 0分 (中性)");
      final_score.FontSize(9);
      final_score.Color(clrWhite);
      final_score.Corner((ENUM_BASE_CORNER)corner);
     }
   delete final_score;
   current_y += LINE_HEIGHT;

   // 下一小时概率
   CChartObjectLabel *next_hour_prob = new CChartObjectLabel();
   if(next_hour_prob.Create(chart_id, prefix + "NextHourProb", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      next_hour_prob.Description("下一小时上涨概率: 50%");
      next_hour_prob.FontSize(9);
      next_hour_prob.Color(clrLightGreen);
      next_hour_prob.Corner((ENUM_BASE_CORNER)corner);
     }
   delete next_hour_prob;
   current_y += LINE_HEIGHT + 5;

   // 分隔线
   CChartObjectLabel *separator9 = new CChartObjectLabel();
   if(separator9.Create(chart_id, prefix + "Separator9", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      separator9.Description("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      separator9.FontSize(8);
      separator9.Color(clrGray);
      separator9.Corner((ENUM_BASE_CORNER)corner);
     }
   delete separator9;
   current_y += LINE_HEIGHT;

   // 状态信息
   CChartObjectLabel *status_label = new CChartObjectLabel();
   if(status_label.Create(chart_id, prefix + "StatusLabel", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      status_label.Description("状态: 初始化中...");
      status_label.FontSize(9);
      status_label.Color(clrOrange);
      status_label.Corner((ENUM_BASE_CORNER)corner);
     }
   delete status_label;
   current_y += LINE_HEIGHT;

   // 最后更新时间
   CChartObjectLabel *update_time = new CChartObjectLabel();
   if(update_time.Create(chart_id, prefix + "UpdateTime", 0, x_offset + LEFT_MARGIN, y_offset + current_y))
     {
      update_time.Description("更新: --:--:--");
      update_time.FontSize(8);
      update_time.Color(clrGray);
      update_time.Corner((ENUM_BASE_CORNER)corner);
     }
   delete update_time;

   Print("UI面板创建完成");
  }

//+------------------------------------------------------------------+
//| 更新静态信息                                                     |
//+------------------------------------------------------------------+
void Panel_UpdateStaticInfo(const long chart_id, const int sample_days)
  {
   string prefix = GetObjectPrefix(chart_id);
   
   // 更新分析天数
   ObjectSetString(chart_id, prefix + "AnalysisDays", OBJPROP_TEXT, "分析样本: " + (string)sample_days + "天");
   
   // 刷新图表
   ChartRedraw(chart_id);
  }

//+------------------------------------------------------------------+
//| 更新面板                                                         |
//+------------------------------------------------------------------+
void Panel_Update(const long chart_id, const DisplayDataPacket &data)
  {
   string prefix = GetObjectPrefix(chart_id);

   // 更新北京时间
   ObjectSetString(chart_id, prefix + "TimeLabel", OBJPROP_TEXT, "当前北京时间: " + data.beijing_time_str);

   // 更新交易时段
   ObjectSetString(chart_id, prefix + "SessionLabel", OBJPROP_TEXT, "当前交易时段: " + data.session_name_str);

   // 更新当前时段统计数据
   string winrate_text = "胜率: ";
   if(data.session_stats.total_samples > 0)
     {
      winrate_text += StringFormat("%.1f%%", data.session_stats.win_rate * 100.0);
     }
   else
     {
      winrate_text += "--";
     }
   ObjectSetString(chart_id, prefix + "CurrentWinRate", OBJPROP_TEXT, winrate_text);

   // 更新样本数
   string samples_text = "样本数: ";
   if(data.session_stats.total_samples > 0)
     {
      samples_text += (string)data.session_stats.total_samples;
     }
   else
     {
      samples_text += "--";
     }
   ObjectSetString(chart_id, prefix + "CurrentSamples", OBJPROP_TEXT, samples_text);

   // 更新平均波幅
   string volatility_text = "平均波幅: ";
   if(data.session_stats.total_samples > 0)
     {
      volatility_text += StringFormat("%.1f pips", data.session_stats.avg_volatility_pips);
     }
   else
     {
      volatility_text += "-- pips";
     }
   ObjectSetString(chart_id, prefix + "CurrentVolatility", OBJPROP_TEXT, volatility_text);

   // 更新周期效应分析
   string weekday_text = "今日(--): 胜率--%";
   if(data.weekday_stats.total_samples > 0)
     {
      // 获取当前星期几的名称
      MqlDateTime dt;
      TimeToStruct(TimeCurrent(), dt);
      string weekday_name = "";
      switch(dt.day_of_week)
        {
         case 0: weekday_name = "周日"; break;
         case 1: weekday_name = "周一"; break;
         case 2: weekday_name = "周二"; break;
         case 3: weekday_name = "周三"; break;
         case 4: weekday_name = "周四"; break;
         case 5: weekday_name = "周五"; break;
         case 6: weekday_name = "周六"; break;
        }
      weekday_text = StringFormat("今日(%s): 胜率%.1f%%", weekday_name, data.weekday_stats.win_rate * 100.0);
     }
   ObjectSetString(chart_id, prefix + "WeekdayStats", OBJPROP_TEXT, weekday_text);

   string month_text = "本月(--): 胜率--%";
   if(data.month_stats.total_samples > 0)
     {
      MqlDateTime dt;
      TimeToStruct(TimeCurrent(), dt);
      month_text = StringFormat("本月(%d月): 胜率%.1f%%", dt.mon, data.month_stats.win_rate * 100.0);
     }
   ObjectSetString(chart_id, prefix + "MonthStats", OBJPROP_TEXT, month_text);

   // 更新波动率分析
   ObjectSetString(chart_id, prefix + "AvgRange", OBJPROP_TEXT,
                   StringFormat("日均波幅: %.1f pips", data.avg_daily_range_pips));

   ObjectSetString(chart_id, prefix + "TodayRange", OBJPROP_TEXT,
                   StringFormat("今日已走: %.1f pips", data.today_range_pips));

   ObjectSetString(chart_id, prefix + "Completion", OBJPROP_TEXT,
                   StringFormat("完成度: %.1f%%", data.completion_percent));

   ObjectSetString(chart_id, prefix + "Remaining", OBJPROP_TEXT,
                   StringFormat("剩余空间: %.1f pips", data.estimated_remaining_pips));

   // 更新走势模式识别
   ObjectSetString(chart_id, prefix + "PatternName", OBJPROP_TEXT,
                   "模式: " + data.pattern_name_str);

   ObjectSetString(chart_id, prefix + "PatternConfidence", OBJPROP_TEXT,
                   StringFormat("置信度: %.1f%%", data.pattern_confidence));

   // 更新智能评分系统
   string score_text = StringFormat("综合评分: %+d分", data.final_score);
   if(data.final_score > 0)
      score_text += " (看涨)";
   else if(data.final_score < 0)
      score_text += " (看跌)";
   else
      score_text += " (中性)";

   ObjectSetString(chart_id, prefix + "FinalScore", OBJPROP_TEXT, score_text);

   // 设置评分颜色
   color score_color = clrWhite;
   if(data.final_score >= 3)
      score_color = clrLime;
   else if(data.final_score >= 1)
      score_color = clrLightGreen;
   else if(data.final_score <= -3)
      score_color = clrRed;
   else if(data.final_score <= -1)
      score_color = clrOrange;

   ObjectSetInteger(chart_id, prefix + "FinalScore", OBJPROP_COLOR, score_color);

   ObjectSetString(chart_id, prefix + "NextHourProb", OBJPROP_TEXT,
                   StringFormat("下一小时上涨概率: %.1f%%", data.next_hour_up_prob));

   // 设置概率颜色
   color prob_color = clrWhite;
   if(data.next_hour_up_prob >= 70.0)
      prob_color = clrLime;
   else if(data.next_hour_up_prob >= 60.0)
      prob_color = clrLightGreen;
   else if(data.next_hour_up_prob <= 30.0)
      prob_color = clrRed;
   else if(data.next_hour_up_prob <= 40.0)
      prob_color = clrOrange;

   ObjectSetInteger(chart_id, prefix + "NextHourProb", OBJPROP_COLOR, prob_color);

   // 更新状态
   ObjectSetString(chart_id, prefix + "StatusLabel", OBJPROP_TEXT, "状态: 运行正常");
   ObjectSetInteger(chart_id, prefix + "StatusLabel", OBJPROP_COLOR, clrLightGreen);

   // 更新最后更新时间
   ObjectSetString(chart_id, prefix + "UpdateTime", OBJPROP_TEXT, "更新: " + data.beijing_time_str);

   // 刷新图表
   ChartRedraw(chart_id);
  }

//+------------------------------------------------------------------+
//| 删除面板                                                         |
//+------------------------------------------------------------------+
void Panel_Delete(const long chart_id)
  {
   string prefix = GetObjectPrefix(chart_id);
   
   // 删除所有相关对象
   ObjectsDeleteAll(chart_id, prefix);
   
   Print("UI面板已清理");
  }

#endif // PANEL_MANAGER_MQH