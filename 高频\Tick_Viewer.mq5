//+------------------------------------------------------------------+
//|                                              Tick_Viewer.mq5 |
//|                    🚀 超高频Tick数据观察器 - 专为高频交易设计    |
//|                                         Created by CodeBuddy |
//+------------------------------------------------------------------+
#property copyright "CodeBuddy"
#property link      "https://github.com"
#property version   "2.0"
#property indicator_chart_window
#property indicator_buffers 0
#property indicator_plots   0

//--- 全局变量，用于追踪上一个价格和时间
double prev_bid = 0;
double prev_ask = 0;
datetime prev_time = 0;
long prev_volume = 0;
long tick_counter = 0;  // 改为long类型避免类型转换警告
datetime start_time = 0;

//+------------------------------------------------------------------+
//| 指标初始化函数
//+------------------------------------------------------------------+
int OnInit() {
    // 设置超高频定时器，每1毫秒检查一次！！！
    EventSetMillisecondTimer(1);
    start_time = TimeCurrent();
    tick_counter = 0;
    
    Print("🚀🚀🚀 超高频Tick数据观察器已启动！专为高频交易优化！");
    Print("⚡ 检查频率: 每1毫秒 (1000Hz)");
    Print("📊 监控品种: ", _Symbol);
    Print("🔥 捕获模式: 每个Tick都不放过！");
    Print("💰 高频交易模式已激活！");
    Print("========================================");
    Print("📖 数据说明:");
    Print("   🔥极速 = 0ms间隔 (同时到达的Tick)");
    Print("   ⚡快速 = 1-10ms间隔 (超高频)");
    Print("   📈正常 = 11-100ms间隔 (高频)");
    Print("   🐌缓慢 = >100ms间隔 (低频)");
    Print("   TPS = Ticks Per Second (每秒Tick数量)");
    Print("   间隔 = 两个Tick之间的时间差(毫秒)");
    Print("========================================");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| OnCalculate函数 - 每个价格变动都会触发
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[]) {
    
    // 在OnCalculate中立即检查Tick数据 - 这是最快的触发方式
    UltraHighFrequencyTickCheck();
    return(rates_total);
}

//+------------------------------------------------------------------+
//| 超高频Tick检查函数 - 专为高频交易优化
//+------------------------------------------------------------------+
void UltraHighFrequencyTickCheck() {
    MqlTick current_tick;
    if(SymbolInfoTick(_Symbol, current_tick)) {
        tick_counter++;
        
        // 初始化
        if(prev_bid == 0 && prev_ask == 0) {
            prev_bid = current_tick.bid;
            prev_ask = current_tick.ask;
            prev_time = current_tick.time;
            prev_volume = current_tick.volume;
            PrintFormat("🚀 [INIT] %s 超高频监控启动 | 买价: %s | 卖价: %s", 
                       _Symbol,
                       DoubleToString(current_tick.bid, _Digits),
                       DoubleToString(current_tick.ask, _Digits));
            return;
        }
        
        // 检查是否有任何变化（价格、时间或成交量）
        bool price_changed = (current_tick.bid != prev_bid || current_tick.ask != prev_ask);
        bool time_changed = (current_tick.time != prev_time);
        bool volume_changed = (current_tick.volume != prev_volume);
        
        if (price_changed || time_changed || volume_changed) {
            
            // 计算时间间隔（毫秒）
            long time_diff_ms = (long)(current_tick.time - prev_time) * 1000;
            
            // 价格方向指示
            string bid_arrow = "→", ask_arrow = "→";
            if(current_tick.bid > prev_bid) bid_arrow = "🚀";
            else if(current_tick.bid < prev_bid) bid_arrow = "🔻";
            
            if(current_tick.ask > prev_ask) ask_arrow = "🚀";
            else if(current_tick.ask < prev_ask) ask_arrow = "🔻";

            // 计算变化量
            double bid_change = current_tick.bid - prev_bid;
            double ask_change = current_tick.ask - prev_ask;
            long volume_change = current_tick.volume - prev_volume;
            int spread_points = (int)((current_tick.ask - current_tick.bid) / _Point);
            
            // 计算运行统计
            long runtime_seconds = TimeCurrent() - start_time;
            double ticks_per_second = runtime_seconds > 0 ? (double)tick_counter / runtime_seconds : 0;
            
            // 根据间隔时间显示不同的紧急程度
            string urgency_indicator = "";
            if(time_diff_ms == 0) urgency_indicator = "🔥极速";
            else if(time_diff_ms <= 10) urgency_indicator = "⚡快速";
            else if(time_diff_ms <= 100) urgency_indicator = "📈正常";
            else urgency_indicator = "🐌缓慢";
            
            PrintFormat("⚡ [#%ld] [%s] %s %s | 买价: %s %s (%+.5f) | 卖价: %s %s (%+.5f) | 点差: %d | 量: %ld (%+ld) | 间隔: %ldms | TPS: %.1f",
                        tick_counter,
                        TimeToString(current_tick.time, TIME_SECONDS),
                        _Symbol,
                        urgency_indicator,
                        DoubleToString(current_tick.bid, _Digits),
                        bid_arrow,
                        bid_change,
                        DoubleToString(current_tick.ask, _Digits),
                        ask_arrow,
                        ask_change,
                        spread_points,
                        current_tick.volume,
                        volume_change,
                        time_diff_ms,
                        ticks_per_second
                       );

            // 更新历史数据
            prev_bid = current_tick.bid;
            prev_ask = current_tick.ask;
            prev_time = current_tick.time;
            prev_volume = current_tick.volume;
        }
    }
}

//+------------------------------------------------------------------+
//| 超高频定时器 - 每1毫秒触发
//+------------------------------------------------------------------+
void OnTimer() {
    UltraHighFrequencyTickCheck();
}

//+------------------------------------------------------------------+
//| 去初始化函数
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    EventKillTimer();
    long runtime = TimeCurrent() - start_time;
    double avg_tps = runtime > 0 ? (double)tick_counter / runtime : 0;
    
    Print("========================================");
    PrintFormat("🏁 超高频Tick观察器已停止");
    PrintFormat("📊 运行时间: %d秒", runtime);
    PrintFormat("📈 总Tick数: %ld", tick_counter);
    PrintFormat("⚡ 平均TPS: %.2f", avg_tps);
    Print("========================================");
}
