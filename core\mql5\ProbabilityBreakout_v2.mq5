//+------------------------------------------------------------------+
//|                                        ProbabilityBreakout_v2.mq5 |
//|                               基于大样本统计的概率突破系统         |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "概率交易系统 v2.0 - 大样本统计版"
#property link      ""
#property version   "2.00"

//--- 输入参数
input group "=== 概率计算参数 ==="
input int      InpHistoryDays = 30;             // 历史数据天数（用于概率计算）
input int      InpMinSampleSize = 20;           // 最小样本数量要求
input int      InpSuccessConfirmBars = 3;       // 成功确认需要的K线数
input double   InpProbThreshold = 0.65;         // 概率阈值

input group "=== 支撑阻力计算 ==="
input int      InpSRLookback = 20;              // 支撑阻力位回看周期
input double   InpSRTolerancePips = 5;          // 支撑阻力位容差（点）

input group "=== 风险管理 ==="
input double   InpRiskPercent = 1.0;            // 每笔交易风险百分比
input double   InpRiskRewardRatio = 2.0;        // 风险回报比
input int      InpMaxConsecutiveLoss = 3;       // 最大连续亏损次数

//--- 全局变量
struct BreakoutEvent
{
    datetime time;           // 突破时间
    double   level;          // 突破水平
    bool     isUpBreakout;   // 是否向上突破
    bool     wasSuccessful;  // 是否成功
    int      maintainBars;   // 维持的K线数
};

BreakoutEvent historyEvents[];   // 历史突破事件
int consecutiveLoss = 0;
bool tradingEnabled = true;

//+------------------------------------------------------------------+
//| 专家顾问初始化函数                                                  |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("概率突破系统 v2.0 初始化 - 大样本统计版");
    
    // 构建历史突破事件数据库
    if(!BuildHistoricalDatabase())
    {
        Print("历史数据库构建失败！");
        return INIT_FAILED;
    }
    
    Print("历史突破事件数据库构建完成，共收集 ", ArraySize(historyEvents), " 个样本");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| 构建历史突破事件数据库                                              |
//+------------------------------------------------------------------+
bool BuildHistoricalDatabase()
{
    // 计算需要的历史数据量
    int totalBars = InpHistoryDays * 24;  // 假设H1图表
    if(_Period == PERIOD_M15) totalBars = InpHistoryDays * 96;
    else if(_Period == PERIOD_H4) totalBars = InpHistoryDays * 6;
    else if(_Period == PERIOD_D1) totalBars = InpHistoryDays;
    
    // 获取历史数据
    double high[], low[], close[];
    datetime time[];
    
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(time, true);
    
    if(CopyHigh(_Symbol, PERIOD_CURRENT, 0, totalBars, high) <= 0 ||
       CopyLow(_Symbol, PERIOD_CURRENT, 0, totalBars, low) <= 0 ||
       CopyClose(_Symbol, PERIOD_CURRENT, 0, totalBars, close) <= 0 ||
       CopyTime(_Symbol, PERIOD_CURRENT, 0, totalBars, time) <= 0)
    {
        Print("历史数据获取失败");
        return false;
    }
    
    // 清空历史事件数组
    ArrayResize(historyEvents, 0);
    
    // 扫描历史数据，寻找突破事件
    for(int i = InpSRLookback + InpSuccessConfirmBars; i < totalBars - 1; i++)
    {
        // 计算当前位置的支撑阻力位
        double resistance = CalculateResistance(high, i, InpSRLookback);
        double support = CalculateSupport(low, i, InpSRLookback);
        
        double tolerancePips = InpSRTolerancePips * _Point;
        
        // 检查向上突破
        if(close[i] > resistance + tolerancePips && close[i+1] <= resistance)
        {
            BreakoutEvent event;
            event.time = time[i];
            event.level = resistance;
            event.isUpBreakout = true;
            event.wasSuccessful = CheckBreakoutSuccess(close, i, resistance, true);
            event.maintainBars = CountMaintainBars(close, i, resistance, true);
            
            // 添加到历史事件
            int size = ArraySize(historyEvents);
            ArrayResize(historyEvents, size + 1);
            historyEvents[size] = event;
        }
        
        // 检查向下突破
        if(close[i] < support - tolerancePips && close[i+1] >= support)
        {
            BreakoutEvent event;
            event.time = time[i];
            event.level = support;
            event.isUpBreakout = false;
            event.wasSuccessful = CheckBreakoutSuccess(close, i, support, false);
            event.maintainBars = CountMaintainBars(close, i, support, false);
            
            // 添加到历史事件
            int size = ArraySize(historyEvents);
            ArrayResize(historyEvents, size + 1);
            historyEvents[size] = event;
        }
    }
    
    // 统计信息
    PrintStatistics();
    
    return true;
}

//+------------------------------------------------------------------+
//| 计算阻力位（更精确的算法）                                           |
//+------------------------------------------------------------------+
double CalculateResistance(const double &high[], int startPos, int lookback)
{
    double maxHigh = 0;
    for(int i = startPos + 1; i <= startPos + lookback; i++)
    {
        if(high[i] > maxHigh)
            maxHigh = high[i];
    }
    return maxHigh;
}

//+------------------------------------------------------------------+
//| 计算支撑位（更精确的算法）                                           |
//+------------------------------------------------------------------+
double CalculateSupport(const double &low[], int startPos, int lookback)
{
    double minLow = 999999;
    for(int i = startPos + 1; i <= startPos + lookback; i++)
    {
        if(low[i] < minLow)
            minLow = low[i];
    }
    return minLow;
}

//+------------------------------------------------------------------+
//| 检查突破是否成功                                                   |
//+------------------------------------------------------------------+
bool CheckBreakoutSuccess(const double &close[], int breakPos, double level, bool isUp)
{
    int successCount = 0;
    
    for(int i = breakPos - 1; i >= breakPos - InpSuccessConfirmBars && i >= 0; i--)
    {
        if((isUp && close[i] > level) || (!isUp && close[i] < level))
            successCount++;
    }
    
    return (successCount >= InpSuccessConfirmBars);
}

//+------------------------------------------------------------------+
//| 计算维持的K线数量                                                 |
//+------------------------------------------------------------------+
int CountMaintainBars(const double &close[], int breakPos, double level, bool isUp)
{
    int count = 0;
    
    for(int i = breakPos - 1; i >= 0; i--)
    {
        if((isUp && close[i] > level) || (!isUp && close[i] < level))
            count++;
        else
            break;
    }
    
    return count;
}

//+------------------------------------------------------------------+
//| 打印统计信息                                                      |
//+------------------------------------------------------------------+
void PrintStatistics()
{
    int totalEvents = ArraySize(historyEvents);
    int upBreakouts = 0, downBreakouts = 0;
    int upSuccess = 0, downSuccess = 0;
    
    for(int i = 0; i < totalEvents; i++)
    {
        if(historyEvents[i].isUpBreakout)
        {
            upBreakouts++;
            if(historyEvents[i].wasSuccessful)
                upSuccess++;
        }
        else
        {
            downBreakouts++;
            if(historyEvents[i].wasSuccessful)
                downSuccess++;
        }
    }
    
    Print("=== 历史突破统计 ===");
    Print("总突破事件: ", totalEvents);
    Print("向上突破: ", upBreakouts, " 次，成功: ", upSuccess, " 次，胜率: ", 
          upBreakouts > 0 ? (double)upSuccess/upBreakouts*100 : 0, "%");
    Print("向下突破: ", downBreakouts, " 次，成功: ", downSuccess, " 次，胜率: ", 
          downBreakouts > 0 ? (double)downSuccess/downBreakouts*100 : 0, "%");
}

//+------------------------------------------------------------------+
//| 计算当前突破的概率                                                 |
//+------------------------------------------------------------------+
double CalculateCurrentBreakoutProbability(bool isUpBreakout, double currentLevel)
{
    if(ArraySize(historyEvents) < InpMinSampleSize)
    {
        Print("历史样本不足，需要至少 ", InpMinSampleSize, " 个样本");
        return 0.0;
    }
    
    // 筛选相关的历史事件
    int relevantEvents = 0;
    int successfulEvents = 0;
    
    double tolerance = InpSRTolerancePips * _Point * 2;  // 扩大容差范围
    
    for(int i = 0; i < ArraySize(historyEvents); i++)
    {
        // 只考虑相同方向的突破
        if(historyEvents[i].isUpBreakout != isUpBreakout)
            continue;
            
        // 只考虑相似水平的突破（可选）
        if(MathAbs(historyEvents[i].level - currentLevel) > tolerance)
            continue;
            
        relevantEvents++;
        if(historyEvents[i].wasSuccessful)
            successfulEvents++;
    }
    
    // 如果相关样本太少，使用全部同方向样本
    if(relevantEvents < InpMinSampleSize / 2)
    {
        relevantEvents = 0;
        successfulEvents = 0;
        
        for(int i = 0; i < ArraySize(historyEvents); i++)
        {
            if(historyEvents[i].isUpBreakout == isUpBreakout)
            {
                relevantEvents++;
                if(historyEvents[i].wasSuccessful)
                    successfulEvents++;
            }
        }
    }
    
    if(relevantEvents == 0)
        return 0.5;  // 默认概率
        
    double probability = (double)successfulEvents / relevantEvents;
    
    Print("概率计算：相关样本=", relevantEvents, ", 成功=", successfulEvents, 
          ", 概率=", probability*100, "%");
    
    return probability;
}

//+------------------------------------------------------------------+
//| 专家顾问主函数                                                     |
//+------------------------------------------------------------------+
void OnTick()
{
    if(!IsNewBar() || !tradingEnabled)
        return;
        
    if(consecutiveLoss >= InpMaxConsecutiveLoss)
    {
        Print("达到最大连续亏损限制，暂停交易");
        return;
    }
    
    if(PositionsTotal() > 0)
        return;
        
    AnalyzeCurrentMarket();
}

//+------------------------------------------------------------------+
//| 分析当前市场                                                      |
//+------------------------------------------------------------------+
void AnalyzeCurrentMarket()
{
    double high[], low[], close[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    
    if(CopyHigh(_Symbol, PERIOD_CURRENT, 0, InpSRLookback + 2, high) <= 0 ||
       CopyLow(_Symbol, PERIOD_CURRENT, 0, InpSRLookback + 2, low) <= 0 ||
       CopyClose(_Symbol, PERIOD_CURRENT, 0, InpSRLookback + 2, close) <= 0)
        return;
        
    double resistance = CalculateResistance(high, 0, InpSRLookback);
    double support = CalculateSupport(low, 0, InpSRLookback);
    double currentPrice = close[0];
    double previousPrice = close[1];
    
    double tolerancePips = InpSRTolerancePips * _Point;
    
    // 检查向上突破
    if(currentPrice > resistance + tolerancePips && previousPrice <= resistance)
    {
        double probability = CalculateCurrentBreakoutProbability(true, resistance);
        if(probability >= InpProbThreshold)
        {
            Print("向上突破概率 ", probability*100, "% 满足条件，准备开多单");
            OpenBuyPosition(resistance);
        }
        else
        {
            Print("向上突破概率 ", probability*100, "% 不满足条件 (要求≥", InpProbThreshold*100, "%)");
        }
    }
    
    // 检查向下突破
    if(currentPrice < support - tolerancePips && previousPrice >= support)
    {
        double probability = CalculateCurrentBreakoutProbability(false, support);
        if(probability >= InpProbThreshold)
        {
            Print("向下突破概率 ", probability*100, "% 满足条件，准备开空单");
            OpenSellPosition(support);
        }
        else
        {
            Print("向下突破概率 ", probability*100, "% 不满足条件 (要求≥", InpProbThreshold*100, "%)");
        }
    }
}

//+------------------------------------------------------------------+
//| 开多单                                                             |
//+------------------------------------------------------------------+
void OpenBuyPosition(double resistance)
{
    double price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double stopLoss = resistance - InpSRTolerancePips * _Point;
    double takeProfit = price + (price - stopLoss) * InpRiskRewardRatio;
    
    double lots = CalculatePositionSize(price - stopLoss);
    
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lots;
    request.type = ORDER_TYPE_BUY;
    request.price = price;
    request.sl = stopLoss;
    request.tp = takeProfit;
    request.comment = "概率突破买入v2";
    
    if(OrderSend(request, result))
    {
        Print("买入订单成功：价格=", price, ", 止损=", stopLoss, ", 止盈=", takeProfit);
    }
    else
    {
        Print("买入订单失败：", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| 开空单                                                             |
//+------------------------------------------------------------------+
void OpenSellPosition(double support)
{
    double price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double stopLoss = support + InpSRTolerancePips * _Point;
    double takeProfit = price - (stopLoss - price) * InpRiskRewardRatio;
    
    double lots = CalculatePositionSize(stopLoss - price);
    
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lots;
    request.type = ORDER_TYPE_SELL;
    request.price = price;
    request.sl = stopLoss;
    request.tp = takeProfit;
    request.comment = "概率突破卖出v2";
    
    if(OrderSend(request, result))
    {
        Print("卖出订单成功：价格=", price, ", 止损=", stopLoss, ", 止盈=", takeProfit);
    }
    else
    {
        Print("卖出订单失败：", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| 计算仓位大小                                                       |
//+------------------------------------------------------------------+
double CalculatePositionSize(double stopLossDistance)
{
    double riskAmount = AccountInfoDouble(ACCOUNT_BALANCE) * InpRiskPercent / 100.0;
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    
    double lots = (riskAmount * tickSize) / (stopLossDistance * tickValue);
    
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lots = MathMax(minLot, MathMin(maxLot, NormalizeDouble(lots / lotStep, 0) * lotStep));
    
    return lots;
}

//+------------------------------------------------------------------+
//| 交易事件处理                                                       |
//+------------------------------------------------------------------+
void OnTrade()
{
    if(HistorySelect(TimeCurrent() - 86400, TimeCurrent()))
    {
        int totalDeals = HistoryDealsTotal();
        if(totalDeals > 0)
        {
            ulong ticket = HistoryDealGetTicket(totalDeals - 1);
            if(HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol)
            {
                double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                
                if(profit < 0)
                {
                    consecutiveLoss++;
                    Print("连续亏损次数：", consecutiveLoss);
                }
                else
                {
                    consecutiveLoss = 0;
                    Print("盈利交易，重置连续亏损计数");
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 检查是否新K线                                                      |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(currentBarTime != lastBarTime)
    {
        lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 专家顾问反初始化函数                                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("概率突破系统 v2.0 停止");
} 