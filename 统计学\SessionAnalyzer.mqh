//+------------------------------------------------------------------+
//|                                              SessionAnalyzer.mqh |
//|                                  时段效应分析模块 - 核心分析引擎 |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef SESSION_ANALYZER_MQH
#define SESSION_ANALYZER_MQH

#include "Defines.mqh"
#include "DataCollector.mqh"

//+------------------------------------------------------------------+
//| 全局变量 - 结果缓存区                                            |
//+------------------------------------------------------------------+
static StatisticalResult g_session_stats[5]; // 以ENUM_TRADING_SESSION为索引的统计结果缓存

//+------------------------------------------------------------------+
//| 主计算函数 - 计算各交易时段的历史统计数据                        |
//+------------------------------------------------------------------+
void Analyzer_CalculateSessionStats(const string symbol)
{
    Print("开始计算时段统计数据...");
    
    // 初始化统计数据
    for(int i = 0; i < 5; i++)
    {
        g_session_stats[i].win_samples = 0;
        g_session_stats[i].total_samples = 0;
        g_session_stats[i].win_rate = 0.0;
        g_session_stats[i].total_volatility_pips = 0.0;
        g_session_stats[i].avg_volatility_pips = 0.0;
    }
    
    // 获取缓存的日线数据数量
    int cache_size = Data_GetCacheSize();
    if(cache_size <= 0)
    {
        Print("错误：没有可用的历史数据进行分析");
        return;
    }
    
    Print("开始分析 ", cache_size, " 天的历史数据");
    
    // 遍历每一天的数据
    for(int day_index = 0; day_index < cache_size; day_index++)
    {
        HistoricalDailyData daily_data = Data_GetDailyDataAt(day_index);
        
        // 获取当天的小时K线数据
        MqlRates h1_rates[];
        datetime day_start = daily_data.date_start_of_day;

        // 使用正确的CopyRates调用方式：从指定时间开始获取24个小时的数据
        int copied = CopyRates(symbol, PERIOD_H1, day_start, 24, h1_rates);
        if(copied <= 0)
        {
            continue; // 跳过无数据的日期
        }
        
        // 分析每个交易时段
        AnalyzeDaySession(h1_rates, copied, SESSION_ASIA);
        AnalyzeDaySession(h1_rates, copied, SESSION_EUROPE);
        AnalyzeDaySession(h1_rates, copied, SESSION_US_EUROPE_CROSS);

        // 美盘后半夜时段需要特殊处理，因为它在当天的开始部分
        AnalyzeDaySession(h1_rates, copied, SESSION_US_NIGHT);
    }
    
    // 计算最终统计结果
    for(int i = 0; i < 5; i++)
    {
        if(g_session_stats[i].total_samples > 0)
        {
            g_session_stats[i].win_rate = (double)g_session_stats[i].win_samples / g_session_stats[i].total_samples;
            g_session_stats[i].avg_volatility_pips = g_session_stats[i].total_volatility_pips / g_session_stats[i].total_samples;
        }
    }
    
    Print("时段统计计算完成");
    PrintSessionStats();
}

//+------------------------------------------------------------------+
//| 分析单日单个时段的数据                                           |
//+------------------------------------------------------------------+
void AnalyzeDaySession(const MqlRates &rates[], int rates_count, ENUM_TRADING_SESSION session)
{
    // 获取时段的北京时间范围
    int start_hour_beijing, end_hour_beijing;
    GetSessionHours(session, start_hour_beijing, end_hour_beijing);

    // 查找时段的开始和结束K线
    int start_index = -1, end_index = -1;

    for(int i = 0; i < rates_count; i++)
    {
        MqlDateTime dt;
        TimeToStruct(rates[i].time + 8*3600, dt); // 转换为北京时间

        // 处理时段开始
        if(start_index == -1 && dt.hour >= start_hour_beijing)
        {
            start_index = i;
        }

        // 处理时段结束
        if(start_index != -1 && dt.hour >= end_hour_beijing)
        {
            end_index = i - 1;
            break;
        }
    }

    // 如果没有找到结束点，说明时段延续到当天结束
    if(end_index == -1 && start_index != -1)
    {
        end_index = rates_count - 1;
    }

    // 验证数据有效性
    if(start_index == -1 || end_index == -1 || start_index > end_index)
    {
        return;
    }
    
    // 计算时段数据
    double open_price = rates[start_index].open;
    double close_price = rates[end_index].close;

    // 计算时段内的最高价和最低价
    double high_price = rates[start_index].high;
    double low_price = rates[start_index].low;

    for(int i = start_index; i <= end_index; i++)
    {
        if(rates[i].high > high_price) high_price = rates[i].high;
        if(rates[i].low < low_price) low_price = rates[i].low;
    }

    // 判断涨跌并更新统计
    bool is_bullish = close_price > open_price;
    double volatility_pips = (high_price - low_price) / _Point;

    // 更新统计数据
    g_session_stats[session].total_samples++;
    if(is_bullish)
    {
        g_session_stats[session].win_samples++;
    }
    g_session_stats[session].total_volatility_pips += volatility_pips;
}

//+------------------------------------------------------------------+
//| 获取交易时段的北京时间小时范围                                   |
//+------------------------------------------------------------------+
void GetSessionHours(ENUM_TRADING_SESSION session, int &start_hour, int &end_hour)
{
    switch(session)
    {
        case SESSION_ASIA:
            start_hour = 7;   // 北京时间7点 (07:00-14:59)
            end_hour = 15;    // 北京时间15点前结束
            break;
        case SESSION_EUROPE:
            start_hour = 15;  // 北京时间15点 (15:00-19:59)
            end_hour = 20;    // 北京时间20点前结束
            break;
        case SESSION_US_EUROPE_CROSS:
            start_hour = 20;  // 北京时间20点 (20:00-23:59)
            end_hour = 24;    // 北京时间24点前结束
            break;
        case SESSION_US_NIGHT:
            start_hour = 0;   // 北京时间0点 (00:00-04:59)
            end_hour = 5;     // 北京时间5点前结束
            break;
        default:
            start_hour = 5;   // 休市时段 (05:00-06:59)
            end_hour = 7;
            break;
    }
}

//+------------------------------------------------------------------+
//| 数据获取接口 - 获取指定时段的统计结果                           |
//+------------------------------------------------------------------+
StatisticalResult Analyzer_GetSessionStats(ENUM_TRADING_SESSION session)
{
    if(session >= 0 && session < 5)
    {
        return g_session_stats[session];
    }
    
    // 返回空的统计结果
    StatisticalResult empty_result;
    empty_result.win_samples = 0;
    empty_result.total_samples = 0;
    empty_result.win_rate = 0.0;
    empty_result.total_volatility_pips = 0.0;
    empty_result.avg_volatility_pips = 0.0;
    
    return empty_result;
}

//+------------------------------------------------------------------+
//| 打印统计结果（调试用）                                           |
//+------------------------------------------------------------------+
void PrintSessionStats()
{
    string session_names[] = {"休市", "亚盘", "欧盘", "欧美交叉", "美盘后半夜"};
    
    Print("=== 时段统计结果 ===");
    for(int i = 0; i < 5; i++)
    {
        Print(StringFormat("%s: 样本数=%d, 胜率=%.1f%%, 平均波幅=%.1f点", 
              session_names[i], 
              g_session_stats[i].total_samples,
              g_session_stats[i].win_rate * 100.0,
              g_session_stats[i].avg_volatility_pips));
    }
}

#endif // SESSION_ANALYZER_MQH