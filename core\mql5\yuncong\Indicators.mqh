//+------------------------------------------------------------------+
//|                                              Indicators.mqh      |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef INDICATORS_MQH
#define INDICATORS_MQH

// 声明全局指标句柄变量
extern int handle_atr;       // ATR指标句柄
extern int handle_volume;    // 成交量指标句柄

//+------------------------------------------------------------------+
//| 初始化所有指标                                                    |
//+------------------------------------------------------------------+
bool InitializeIndicators()
{
   // 初始化ATR指标
   handle_atr = iATR(_Symbol, PERIOD_CURRENT, 14);
   if(handle_atr == INVALID_HANDLE)
   {
      Print("初始化ATR指标失败");
      return false;
   }
   
   // 初始化成交量指标
   handle_volume = iVolumes(_Symbol, PERIOD_CURRENT, VOLUME_TICK);
   if(handle_volume == INVALID_HANDLE)
   {
      Print("初始化成交量指标失败");
      return false;
   }
   
   Print("所有指标初始化成功");
   return true;
}

//+------------------------------------------------------------------+
//| 释放所有指标句柄                                                  |
//+------------------------------------------------------------------+
void DeinitializeIndicators()
{
   // 释放指标句柄
   if(handle_atr != INVALID_HANDLE)
      IndicatorRelease(handle_atr);
      
   if(handle_volume != INVALID_HANDLE)
      IndicatorRelease(handle_volume);
      
   Print("所有指标句柄已释放");
}

#endif // INDICATORS_MQH