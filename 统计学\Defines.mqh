//+------------------------------------------------------------------+
//|                                                      Defines.mqh |
//|                                  动态概率统计分析系统核心定义文件 |
//|                                                                  |
//+------------------------------------------------------------------+

#ifndef DEFINES_MQH
#define DEFINES_MQH

//+------------------------------------------------------------------+
//| 交易时段枚举                                                     |
//+------------------------------------------------------------------+
enum ENUM_TRADING_SESSION
  {
   SESSION_NONE,               // 休市时段
   SESSION_ASIA,               // 亚盘
   SESSION_EUROPE,             // 欧盘
   SESSION_US_EUROPE_CROSS,    // 欧美交叉
   SESSION_US_NIGHT            // 美盘后半夜
  };

//+------------------------------------------------------------------+
//| 日度数据结构体                                                   |
//+------------------------------------------------------------------+
struct HistoricalDailyData
  {
   datetime date_start_of_day; // 当日零点时间戳
   double   open;              // 开盘价
   double   high;              // 最高价
   double   low;               // 最低价
   double   close;             // 收盘价
   int      day_of_week;       // 星期几 (1=周一, 7=周日)
   int      month_of_year;     // 月份 (1-12)
  };

//+------------------------------------------------------------------+
//| 统计结果结构体                                                   |
//+------------------------------------------------------------------+
struct StatisticalResult
  {
   int      total_samples;       // 总样本数
   int      win_samples;         // 上涨/成功样本数
   double   win_rate;            // 胜率 (0.0-1.0)
   double   total_volatility_pips; // 累计总波幅 (点数)
   double   avg_volatility_pips; // 平均波幅 (点数)
  };

//+------------------------------------------------------------------+
//| 日内走势模式枚举                                                 |
//+------------------------------------------------------------------+
enum ENUM_INTRADAY_PATTERN
  {
   PATTERN_UNKNOWN,             // 未知模式
   PATTERN_BULL_TREND,          // 一直上涨
   PATTERN_BEAR_TREND,          // 一直下跌
   PATTERN_INVERTED_V,          // 倒V型(先涨后跌)
   PATTERN_V_REVERSAL,          // V型反转(先跌后涨)
   PATTERN_SIDEWAYS,            // 震荡整理
   PATTERN_MORNING_STAR,        // 晨星模式
   PATTERN_EVENING_STAR,        // 暮星模式
   PATTERN_DOJI_INDECISION      // 十字星犹豫
  };

//+------------------------------------------------------------------+
//| 显示数据包结构体                                                 |
//+------------------------------------------------------------------+
struct DisplayDataPacket
  {
   string                  beijing_time_str;        // 北京时间字符串
   string                  session_name_str;        // 时段名称字符串
   StatisticalResult       session_stats;           // 时段统计结果
   StatisticalResult       weekday_stats;           // 星期统计结果
   StatisticalResult       month_stats;             // 月份统计结果
   double                  avg_daily_range_pips;    // 历史日均波幅
   double                  today_range_pips;        // 今日已走波幅
   double                  estimated_remaining_pips; // 预估剩余空间
   double                  completion_percent;      // 波幅完成百分比
   ENUM_INTRADAY_PATTERN   pattern_type;            // 模式枚举
   string                  pattern_name_str;        // 模式名称
   double                  pattern_confidence;      // 置信度
   int                     final_score;             // 综合评分
   double                  next_hour_up_prob;       // 下一小时上涨概率
  };

#endif // DEFINES_MQH