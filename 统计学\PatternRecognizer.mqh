//+------------------------------------------------------------------+
//|                                            PatternRecognizer.mqh |
//|                                动态概率统计分析系统走势模式识别器 |
//|                                                                  |
//+------------------------------------------------------------------+

#ifndef PATTERN_RECOGNIZER_MQH
#define PATTERN_RECOGNIZER_MQH

#include "Defines.mqh"

//+------------------------------------------------------------------+
//| 获取模式名称                                                     |
//+------------------------------------------------------------------+
string Recognizer_GetPatternName(ENUM_INTRADAY_PATTERN pattern)
  {
   switch(pattern)
     {
      case PATTERN_BULL_TREND:      return "一直上涨";
      case PATTERN_BEAR_TREND:      return "一直下跌";
      case PATTERN_INVERTED_V:      return "倒V型";
      case PATTERN_V_REVERSAL:      return "V型反转";
      case PATTERN_SIDEWAYS:        return "震荡整理";
      case PATTERN_MORNING_STAR:    return "晨星模式";
      case PATTERN_EVENING_STAR:    return "暮星模式";
      case PATTERN_DOJI_INDECISION: return "十字星犹豫";
      default:                      return "未知模式";
     }
  }

//+------------------------------------------------------------------+
//| 计算价格接近度（返回0-100的相似度）                              |
//+------------------------------------------------------------------+
double CalculateProximity(double price1, double price2, double range)
  {
   if(range <= 0)
      return 0.0;
   
   double diff = MathAbs(price1 - price2);
   double proximity = (1.0 - (diff / range)) * 100.0;
   
   return MathMax(0.0, MathMin(100.0, proximity));
  }

//+------------------------------------------------------------------+
//| 识别一直上涨模式                                                 |
//+------------------------------------------------------------------+
double RecognizeBullTrend(double open, double high, double low, double close, double range)
  {
   // 条件：收盘价接近最高价，开盘价接近最低价，实体为阳线
   if(close <= open)
      return 0.0;  // 必须是阳线
   
   double close_to_high = CalculateProximity(close, high, range);
   double open_to_low = CalculateProximity(open, low, range);
   double body_size = ((close - open) / range) * 100.0;
   
   // 综合评分
   double confidence = (close_to_high * 0.4 + open_to_low * 0.4 + body_size * 0.2);
   
   return MathMin(100.0, confidence);
  }

//+------------------------------------------------------------------+
//| 识别一直下跌模式                                                 |
//+------------------------------------------------------------------+
double RecognizeBearTrend(double open, double high, double low, double close, double range)
  {
   // 条件：收盘价接近最低价，开盘价接近最高价，实体为阴线
   if(close >= open)
      return 0.0;  // 必须是阴线
   
   double close_to_low = CalculateProximity(close, low, range);
   double open_to_high = CalculateProximity(open, high, range);
   double body_size = ((open - close) / range) * 100.0;
   
   // 综合评分
   double confidence = (close_to_low * 0.4 + open_to_high * 0.4 + body_size * 0.2);
   
   return MathMin(100.0, confidence);
  }

//+------------------------------------------------------------------+
//| 识别倒V型模式（先涨后跌）                                        |
//+------------------------------------------------------------------+
double RecognizeInvertedV(double open, double high, double low, double close, double range)
  {
   // 条件：最高价显著高于开盘价和收盘价，且开盘价和收盘价接近
   double high_prominence = ((high - MathMax(open, close)) / range) * 100.0;
   double open_close_proximity = CalculateProximity(open, close, range);
   
   // 要求最高价突出且开收盘价接近
   if(high_prominence < 30.0)
      return 0.0;
   
   double confidence = (high_prominence * 0.6 + open_close_proximity * 0.4);
   
   return MathMin(100.0, confidence);
  }

//+------------------------------------------------------------------+
//| 识别V型反转模式（先跌后涨）                                      |
//+------------------------------------------------------------------+
double RecognizeVReversal(double open, double high, double low, double close, double range)
  {
   // 条件：最低价显著低于开盘价和收盘价，且开盘价和收盘价接近
   double low_prominence = ((MathMin(open, close) - low) / range) * 100.0;
   double open_close_proximity = CalculateProximity(open, close, range);
   
   // 要求最低价突出且开收盘价接近
   if(low_prominence < 30.0)
      return 0.0;
   
   double confidence = (low_prominence * 0.6 + open_close_proximity * 0.4);
   
   return MathMin(100.0, confidence);
  }

//+------------------------------------------------------------------+
//| 识别震荡整理模式                                                 |
//+------------------------------------------------------------------+
double RecognizeSideways(double open, double high, double low, double close, double range, double avg_range)
  {
   // 条件：波幅远小于历史平均波幅，且收盘价非常接近开盘价
   if(avg_range <= 0)
      return 0.0;
   
   double range_ratio = (range / avg_range) * 100.0;
   double open_close_proximity = CalculateProximity(open, close, range);
   
   // 要求波幅小于平均值的70%
   if(range_ratio > 70.0)
      return 0.0;
   
   double confidence = ((100.0 - range_ratio) * 0.5 + open_close_proximity * 0.5);
   
   return MathMin(100.0, confidence);
  }

//+------------------------------------------------------------------+
//| 识别十字星犹豫模式                                               |
//+------------------------------------------------------------------+
double RecognizeDojiIndecision(double open, double high, double low, double close, double range)
  {
   // 条件：开盘价和收盘价非常接近，上下影线较长
   double open_close_proximity = CalculateProximity(open, close, range);
   double body_size = (MathAbs(close - open) / range) * 100.0;
   
   // 要求实体很小
   if(body_size > 10.0)
      return 0.0;
   
   double upper_shadow = ((high - MathMax(open, close)) / range) * 100.0;
   double lower_shadow = ((MathMin(open, close) - low) / range) * 100.0;
   
   double confidence = (open_close_proximity * 0.4 + (100.0 - body_size) * 0.3 + 
                       (upper_shadow + lower_shadow) * 0.3);
   
   return MathMin(100.0, confidence);
  }

//+------------------------------------------------------------------+
//| 主识别函数                                                       |
//+------------------------------------------------------------------+
void Recognizer_IdentifyPattern(const string symbol, ENUM_INTRADAY_PATTERN &pattern, string &name, double &confidence)
  {
   // 获取当日的OHLC数据
   double open = iOpen(symbol, PERIOD_D1, 0);
   double high = iHigh(symbol, PERIOD_D1, 0);
   double low = iLow(symbol, PERIOD_D1, 0);
   double close = iClose(symbol, PERIOD_D1, 0);
   
   // 检查数据有效性
   if(open == 0 || high == 0 || low == 0 || close == 0)
     {
      pattern = PATTERN_UNKNOWN;
      name = "数据无效";
      confidence = 0.0;
      return;
     }
   
   double range = high - low;
   if(range <= 0)
     {
      pattern = PATTERN_UNKNOWN;
      name = "无波动";
      confidence = 0.0;
      return;
     }
   
   // 获取历史平均波幅用于震荡判断
   double avg_range = 0.0;
   // 这里可以调用VolatilityAnalyzer的函数，但为了避免循环依赖，我们简化处理
   
   // 测试各种模式
   double bull_conf = RecognizeBullTrend(open, high, low, close, range);
   double bear_conf = RecognizeBearTrend(open, high, low, close, range);
   double inv_v_conf = RecognizeInvertedV(open, high, low, close, range);
   double v_rev_conf = RecognizeVReversal(open, high, low, close, range);
   double sideways_conf = RecognizeSideways(open, high, low, close, range, avg_range);
   double doji_conf = RecognizeDojiIndecision(open, high, low, close, range);
   
   // 找出置信度最高的模式
   double max_confidence = 0.0;
   ENUM_INTRADAY_PATTERN best_pattern = PATTERN_UNKNOWN;
   
   if(bull_conf > max_confidence)
     {
      max_confidence = bull_conf;
      best_pattern = PATTERN_BULL_TREND;
     }
   
   if(bear_conf > max_confidence)
     {
      max_confidence = bear_conf;
      best_pattern = PATTERN_BEAR_TREND;
     }
   
   if(inv_v_conf > max_confidence)
     {
      max_confidence = inv_v_conf;
      best_pattern = PATTERN_INVERTED_V;
     }
   
   if(v_rev_conf > max_confidence)
     {
      max_confidence = v_rev_conf;
      best_pattern = PATTERN_V_REVERSAL;
     }
   
   if(sideways_conf > max_confidence)
     {
      max_confidence = sideways_conf;
      best_pattern = PATTERN_SIDEWAYS;
     }
   
   if(doji_conf > max_confidence)
     {
      max_confidence = doji_conf;
      best_pattern = PATTERN_DOJI_INDECISION;
     }
   
   // 设置最小置信度阈值
   if(max_confidence < 30.0)
     {
      best_pattern = PATTERN_UNKNOWN;
      max_confidence = 0.0;
     }
   
   pattern = best_pattern;
   name = Recognizer_GetPatternName(pattern);
   confidence = max_confidence;
  }

#endif // PATTERN_RECOGNIZER_MQH
