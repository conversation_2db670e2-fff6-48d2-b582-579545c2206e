//+------------------------------------------------------------------+
//|                                               ScoringSystem.mqh |
//|                                动态概率统计分析系统智能评分系统 |
//|                                                                  |
//+------------------------------------------------------------------+

#ifndef SCORING_SYSTEM_MQH
#define SCORING_SYSTEM_MQH

#include "Defines.mqh"

//+------------------------------------------------------------------+
//| 计算时段评分（-2到+2）                                           |
//+------------------------------------------------------------------+
int CalculateSessionScore(const StatisticalResult &session_stats)
  {
   if(session_stats.total_samples < 10)
      return 0;  // 样本数太少，不予评分
   
   double win_rate = session_stats.win_rate;
   
   if(win_rate >= 0.65)
      return 2;   // 强烈看涨
   else if(win_rate >= 0.60)
      return 1;   // 看涨
   else if(win_rate >= 0.55)
      return 0;   // 中性偏涨
   else if(win_rate >= 0.45)
      return 0;   // 中性
   else if(win_rate >= 0.40)
      return -1;  // 看跌
   else
      return -2;  // 强烈看跌
  }

//+------------------------------------------------------------------+
//| 计算星期评分（-1到+1）                                           |
//+------------------------------------------------------------------+
int CalculateWeekdayScore(const StatisticalResult &weekday_stats)
  {
   if(weekday_stats.total_samples < 5)
      return 0;  // 样本数太少，不予评分
   
   double win_rate = weekday_stats.win_rate;
   
   if(win_rate >= 0.60)
      return 1;   // 看涨
   else if(win_rate >= 0.55)
      return 0;   // 中性偏涨
   else if(win_rate >= 0.45)
      return 0;   // 中性
   else if(win_rate >= 0.40)
      return -1;  // 看跌
   else
      return -1;  // 强烈看跌
  }

//+------------------------------------------------------------------+
//| 计算月份评分（-1到+1）                                           |
//+------------------------------------------------------------------+
int CalculateMonthScore(const StatisticalResult &month_stats)
  {
   if(month_stats.total_samples < 5)
      return 0;  // 样本数太少，不予评分
   
   double win_rate = month_stats.win_rate;
   
   if(win_rate >= 0.60)
      return 1;   // 看涨
   else if(win_rate >= 0.55)
      return 0;   // 中性偏涨
   else if(win_rate >= 0.45)
      return 0;   // 中性
   else if(win_rate >= 0.40)
      return -1;  // 看跌
   else
      return -1;  // 强烈看跌
  }

//+------------------------------------------------------------------+
//| 计算模式评分（-2到+2）                                           |
//+------------------------------------------------------------------+
int CalculatePatternScore(ENUM_INTRADAY_PATTERN pattern_type, double pattern_confidence)
  {
   // 如果置信度太低，不予评分
   if(pattern_confidence < 50.0)
      return 0;
   
   int base_score = 0;
   
   switch(pattern_type)
     {
      case PATTERN_BULL_TREND:
         base_score = 2;   // 强烈看涨
         break;
      case PATTERN_V_REVERSAL:
         base_score = 1;   // 看涨
         break;
      case PATTERN_MORNING_STAR:
         base_score = 1;   // 看涨
         break;
      case PATTERN_BEAR_TREND:
         base_score = -2;  // 强烈看跌
         break;
      case PATTERN_INVERTED_V:
         base_score = -1;  // 看跌
         break;
      case PATTERN_EVENING_STAR:
         base_score = -1;  // 看跌
         break;
      case PATTERN_SIDEWAYS:
         base_score = 0;   // 中性
         break;
      case PATTERN_DOJI_INDECISION:
         base_score = 0;   // 中性
         break;
      default:
         base_score = 0;   // 未知模式
         break;
     }
   
   // 根据置信度调整评分
   if(pattern_confidence >= 80.0)
     {
      return base_score;  // 高置信度，保持原评分
     }
   else if(pattern_confidence >= 60.0)
     {
      // 中等置信度，评分减半
      if(base_score > 0)
         return MathMax(1, base_score / 2);
      else if(base_score < 0)
         return MathMin(-1, base_score / 2);
      else
         return 0;
     }
   else
     {
      return 0;  // 低置信度，不予评分
     }
  }

//+------------------------------------------------------------------+
//| 计算波动率调整因子（-1到+1）                                     |
//+------------------------------------------------------------------+
int CalculateVolatilityAdjustment(double completion_percent)
  {
   if(completion_percent < 30.0)
     {
      return 1;   // 波动率低，有上涨空间
     }
   else if(completion_percent > 120.0)
     {
      return -1;  // 波动率过高，可能回调
     }
   else
     {
      return 0;   // 正常波动率
     }
  }

//+------------------------------------------------------------------+
//| 主评分函数                                                       |
//+------------------------------------------------------------------+
void Scorer_Calculate(const DisplayDataPacket &data, int &score, double &prob)
  {
   // 初始化评分
   score = 0;
   
   // 1. 时段评分（-2到+2）
   int session_score = CalculateSessionScore(data.session_stats);
   score += session_score;
   
   // 2. 星期评分（-1到+1）
   int weekday_score = CalculateWeekdayScore(data.weekday_stats);
   score += weekday_score;
   
   // 3. 月份评分（-1到+1）
   int month_score = CalculateMonthScore(data.month_stats);
   score += month_score;
   
   // 4. 模式评分（-2到+2）
   int pattern_score = CalculatePatternScore(data.pattern_type, data.pattern_confidence);
   score += pattern_score;
   
   // 5. 波动率调整（-1到+1）
   int volatility_adjustment = CalculateVolatilityAdjustment(data.completion_percent);
   score += volatility_adjustment;
   
   // 确保评分在-6到+6范围内
   score = MathMax(-6, MathMin(6, score));
   
   // 6. 计算概率：将-6到+6的评分线性映射到10%-90%的概率
   // 公式: prob = 50.0 + (score / 6.0) * 40.0
   prob = 50.0 + ((double)score / 6.0) * 40.0;
   
   // 确保概率在合理范围内
   prob = MathMax(10.0, MathMin(90.0, prob));
  }

//+------------------------------------------------------------------+
//| 获取评分描述                                                     |
//+------------------------------------------------------------------+
string Scorer_GetScoreDescription(int score)
  {
   switch(score)
     {
      case 6:  return "极强看涨";
      case 5:  return "强烈看涨";
      case 4:  return "明显看涨";
      case 3:  return "看涨";
      case 2:  return "偏涨";
      case 1:  return "微涨";
      case 0:  return "中性";
      case -1: return "微跌";
      case -2: return "偏跌";
      case -3: return "看跌";
      case -4: return "明显看跌";
      case -5: return "强烈看跌";
      case -6: return "极强看跌";
      default: return "未知";
     }
  }

//+------------------------------------------------------------------+
//| 获取概率等级描述                                                 |
//+------------------------------------------------------------------+
string Scorer_GetProbabilityLevel(double probability)
  {
   if(probability >= 80.0)
      return "极高概率";
   else if(probability >= 70.0)
      return "高概率";
   else if(probability >= 60.0)
      return "较高概率";
   else if(probability >= 55.0)
      return "略高概率";
   else if(probability >= 45.0)
      return "中性概率";
   else if(probability >= 40.0)
      return "略低概率";
   else if(probability >= 30.0)
      return "较低概率";
   else if(probability >= 20.0)
      return "低概率";
   else
      return "极低概率";
  }

//+------------------------------------------------------------------+
//| 获取交易建议                                                     |
//+------------------------------------------------------------------+
string Scorer_GetTradingAdvice(int score, double probability)
  {
   if(score >= 4 && probability >= 70.0)
      return "强烈建议做多";
   else if(score >= 2 && probability >= 60.0)
      return "建议做多";
   else if(score >= 1 && probability >= 55.0)
      return "可考虑做多";
   else if(score <= -4 && probability <= 30.0)
      return "强烈建议做空";
   else if(score <= -2 && probability <= 40.0)
      return "建议做空";
   else if(score <= -1 && probability <= 45.0)
      return "可考虑做空";
   else
      return "建议观望";
  }

//+------------------------------------------------------------------+
//| 获取详细评分分析                                                 |
//+------------------------------------------------------------------+
string Scorer_GetDetailedAnalysis(const DisplayDataPacket &data, int final_score)
  {
   string analysis = "评分详情:\n";
   
   // 时段评分
   int session_score = CalculateSessionScore(data.session_stats);
   analysis += StringFormat("时段效应: %+d分 (胜率%.1f%%)\n", 
                           session_score, data.session_stats.win_rate * 100);
   
   // 星期评分
   int weekday_score = CalculateWeekdayScore(data.weekday_stats);
   analysis += StringFormat("星期效应: %+d分 (胜率%.1f%%)\n", 
                           weekday_score, data.weekday_stats.win_rate * 100);
   
   // 月份评分
   int month_score = CalculateMonthScore(data.month_stats);
   analysis += StringFormat("月份效应: %+d分 (胜率%.1f%%)\n", 
                           month_score, data.month_stats.win_rate * 100);
   
   // 模式评分
   int pattern_score = CalculatePatternScore(data.pattern_type, data.pattern_confidence);
   analysis += StringFormat("模式识别: %+d分 (%s, %.1f%%)\n", 
                           pattern_score, data.pattern_name_str, data.pattern_confidence);
   
   // 波动率调整
   int volatility_adjustment = CalculateVolatilityAdjustment(data.completion_percent);
   analysis += StringFormat("波动率调整: %+d分 (完成度%.1f%%)\n", 
                           volatility_adjustment, data.completion_percent);
   
   analysis += StringFormat("总评分: %+d分", final_score);
   
   return analysis;
  }

#endif // SCORING_SYSTEM_MQH
