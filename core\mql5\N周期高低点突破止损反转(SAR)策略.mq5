//+------------------------------------------------------------------+
//|                                NPeriodBreakoutSAR_EA_CN.mq5      |
//|                                  Copyright 2023, MetaQuotes Ltd. |
//|                                 https://www.metaquotes.net       |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Ltd."
#property link      "https://www.metaquotes.net"
#property version   "1.00"
#property strict
#property description "N周期高低点突破止损反转EA - 基于前N周期高低点的突破策略"

// 引入交易操作所需的库
#include <Trade\Trade.mqh>

// 定义输入参数
input int    N_Periods = 10;         // 用于确定突破水平的前期K线周期数
input double LotSize = 0.01;         // 交易的固定手数
input long   MagicNumber = 12345;    // EA所下订单的魔术号（幻数）
input uint   Slippage = 5;           // 订单执行时允许的滑点（点数）
input int    StopLossPips = 0;       // 止损点数（0 = 不设置止损）
input int    TakeProfitPips = 0;     // 止盈点数（0 = 不设置止盈）

// 全局变量
CTrade trade;                        // 交易操作对象
datetime lastBarTime = 0;            // 上一次处理的K线时间
double point;                        // 点值
int digits;                          // 价格小数位数

//+------------------------------------------------------------------+
//| 专家顾问初始化函数                                               |
//+------------------------------------------------------------------+
int OnInit()
{
   // 检查输入参数有效性
   if(N_Periods <= 0)
   {
      Print("错误: N_Periods必须大于0");
      return INIT_PARAMETERS_INCORRECT;
   }
   
   if(LotSize <= 0)
   {
      Print("错误: LotSize必须大于0");
      return INIT_PARAMETERS_INCORRECT;
   }
   
   // 初始化交易对象
   trade.SetExpertMagicNumber(MagicNumber);
   trade.SetDeviationInPoints(Slippage);
   trade.SetMarginMode();
   trade.LogLevel(LOG_LEVEL_ERRORS);
   
   // 获取当前品种的点值和小数位数
   digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
   point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   
   // 输出初始化信息
   Print("N周期高低点突破止损反转EA初始化成功");
   Print("交易品种: ", _Symbol, ", 周期: ", EnumToString(PERIOD_CURRENT));
   Print("周期数: ", N_Periods, ", 交易手数: ", LotSize);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 专家顾问去初始化函数                                             |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 输出去初始化信息
   Print("N周期高低点突破止损反转EA已停止, 原因代码: ", reason);
}

//+------------------------------------------------------------------+
//| 专家顾问报价函数                                                 |
//+------------------------------------------------------------------+
void OnTick()
{
   // 获取当前K线信息
   MqlRates rates[];
   if(CopyRates(_Symbol, PERIOD_CURRENT, 0, N_Periods + 1, rates) < N_Periods + 1)
   {
      Print("无法获取足够的K线数据");
      return;
   }
   
   // 检查是否是新K线
   datetime currentBarTime = rates[0].time;
   if(currentBarTime == lastBarTime)
      return; // 如果不是新K线，则退出
   
   lastBarTime = currentBarTime;
   
   // 计算前N周期的最高点和最低点
   double prev_N_highest_high = 0;
   double prev_N_lowest_low = DBL_MAX;
   
   // 注意：从索引1开始，因为索引0是当前正在形成的K线
   for(int i = 1; i <= N_Periods; i++)
   {
      if(rates[i].high > prev_N_highest_high)
         prev_N_highest_high = rates[i].high;
         
      if(rates[i].low < prev_N_lowest_low)
         prev_N_lowest_low = rates[i].low;
   }
   
   // 获取当前价格
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   
   // 输出调试信息
   Print("前", N_Periods, "周期最高点: ", prev_N_highest_high, ", 最低点: ", prev_N_lowest_low);
   Print("当前卖出价(Ask): ", ask, ", 当前买入价(Bid): ", bid);
   
   // 检查是否有持仓
   bool hasLongPosition = false;
   bool hasShortPosition = false;
   double positionVolume = 0;
   
   // 遍历所有持仓
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket <= 0)
         continue;
         
      // 只检查当前EA的持仓
      if(PositionGetInteger(POSITION_MAGIC) != MagicNumber)
         continue;
         
      // 只检查当前交易品种的持仓
      if(PositionGetString(POSITION_SYMBOL) != _Symbol)
         continue;
         
      ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
      positionVolume = PositionGetDouble(POSITION_VOLUME);
      
      if(posType == POSITION_TYPE_BUY)
         hasLongPosition = true;
      else if(posType == POSITION_TYPE_SELL)
         hasShortPosition = true;
   }
   
   // 计算止损和止盈价格（如果启用）
   double stopLossPrice = 0;
   double takeProfitPrice = 0;
   
   // 执行交易逻辑
   
   // 向上突破前N周期最高点 - 做多信号
   if(ask > prev_N_highest_high)
   {
      // 如果有空头持仓，先平仓
      if(hasShortPosition)
      {
         Print("价格突破前", N_Periods, "周期最高点，平掉空头仓位");
         if(!trade.PositionClose(_Symbol))
         {
            Print("平仓失败，错误代码: ", trade.ResultRetcode(), ", 描述: ", trade.ResultComment());
         }
         else
         {
            Print("空头平仓成功");
         }
      }
      
      // 如果没有多头持仓，开立多头仓位
      if(!hasLongPosition)
      {
         // 计算止损和止盈价格（如果启用）
         if(StopLossPips > 0)
            stopLossPrice = bid - StopLossPips * point * 10;
            
         if(TakeProfitPips > 0)
            takeProfitPrice = bid + TakeProfitPips * point * 10;
            
         Print("价格突破前", N_Periods, "周期最高点，开立多头仓位");
         if(!trade.Buy(LotSize, _Symbol, 0, stopLossPrice, takeProfitPrice))
         {
            Print("开仓失败，错误代码: ", trade.ResultRetcode(), ", 描述: ", trade.ResultComment());
         }
         else
         {
            Print("多头开仓成功，手数: ", LotSize);
         }
      }
   }
   
   // 向下突破前N周期最低点 - 做空信号
   else if(bid < prev_N_lowest_low)
   {
      // 如果有多头持仓，先平仓
      if(hasLongPosition)
      {
         Print("价格突破前", N_Periods, "周期最低点，平掉多头仓位");
         if(!trade.PositionClose(_Symbol))
         {
            Print("平仓失败，错误代码: ", trade.ResultRetcode(), ", 描述: ", trade.ResultComment());
         }
         else
         {
            Print("多头平仓成功");
         }
      }
      
      // 如果没有空头持仓，开立空头仓位
      if(!hasShortPosition)
      {
         // 计算止损和止盈价格（如果启用）
         if(StopLossPips > 0)
            stopLossPrice = ask + StopLossPips * point * 10;
            
         if(TakeProfitPips > 0)
            takeProfitPrice = ask - TakeProfitPips * point * 10;
            
         Print("价格突破前", N_Periods, "周期最低点，开立空头仓位");
         if(!trade.Sell(LotSize, _Symbol, 0, stopLossPrice, takeProfitPrice))
         {
            Print("开仓失败，错误代码: ", trade.ResultRetcode(), ", 描述: ", trade.ResultComment());
         }
         else
         {
            Print("空头开仓成功，手数: ", LotSize);
         }
      }
   }
}
//+------------------------------------------------------------------+