from typing import Dict, List, Tuple
import pandas as pd
import numpy as np
import logging
from utils.time_utils import timeit

# 设置日志
logger = logging.getLogger(__name__)

class PatternAnalyzer:
    """
    技术形态分析器 - 平台无关的核心分析逻辑
    
    该类实现了各种技术分析形态的识别，包括：
    - 突破前的盘整
    - 假突破回踩
    - 日内趋势延续
    - 动量突破
    - 关键价位反转
    - 高低点突破
    - 成交量价格背离
    - 日内支撑阻力测试
    - 波动率收缩扩张
    - 开盘缺口填补
    """
    
    def __init__(self) -> None:
        self.min_bars: int = 10  # 日内分析最少需要的K线数量
        self.error_margin: float = 0.0005  # 日内交易误差容忍度更小
        self.volume_threshold: float = 1.5  # 成交量放大阈值
        self.price_threshold: float = 0.001  # 价格变动阈值(0.1%)
        
    def analyze_consolidation_breakout(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        盘整突破分析
        
        分析价格在窄幅区间盘整后的突破行为，这是日内交易中常见的机会。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'open', 'high', 'low', 'close', 'tick_volume'
        
        返回:
            Tuple[bool, str]: (是否形成盘整突破, 详细信息)
        
        分析逻辑:
        1. 检测前期是否存在窄幅盘整
        2. 判断当前K线是否有效突破盘整区间
        3. 确认突破的成交量是否放大
        """
        try:
            if len(data) < self.min_bars:
                return False, "数据量不足"
                
            # 使用最近的数据进行分析
            recent_data = data.tail(self.min_bars).copy()
            
            # 计算前期盘整区间(不包括最后一根K线)
            consolidation_data = recent_data.iloc[:-1]
            consolidation_high = consolidation_data['high'].max()
            consolidation_low = consolidation_data['low'].min()
            consolidation_range = consolidation_high - consolidation_low
            
            # 计算平均K线波动范围
            avg_candle_range = (consolidation_data['high'] - consolidation_data['low']).mean()
            
            # 判断是否为窄幅盘整(盘整区间不超过平均K线波动的3倍)
            is_tight_range = consolidation_range < avg_candle_range * 3
            
            if not is_tight_range:
                return False, "不符合窄幅盘整条件"
            
            # 获取最新K线数据
            current_bar = recent_data.iloc[-1]
            
            # 判断是否突破
            breakout_up = current_bar['close'] > consolidation_high and current_bar['open'] < consolidation_high
            breakout_down = current_bar['close'] < consolidation_low and current_bar['open'] > consolidation_low
            
            # 计算成交量放大情况
            avg_volume = consolidation_data['tick_volume'].mean()
            volume_surge = current_bar['tick_volume'] > avg_volume * self.volume_threshold
            
            # 计算突破幅度
            if breakout_up:
                breakout_size = (current_bar['close'] - consolidation_high) / consolidation_high
                direction = "上行"
            elif breakout_down:
                breakout_size = (consolidation_low - current_bar['close']) / consolidation_low
                direction = "下行"
            else:
                return False, "未形成有效突破"
            
            # 判断突破是否有效
            valid_breakout = (breakout_up or breakout_down) and volume_surge and breakout_size > self.price_threshold
            
            if valid_breakout:
                info = (
                    f"{direction}突破 - "
                    f"盘整区间: {consolidation_low:.6f}-{consolidation_high:.6f}, "
                    f"突破幅度: {breakout_size*100:.2f}%, "
                    f"成交量: 当前{current_bar['tick_volume']:.0f}/平均{avg_volume:.0f} "
                    f"(放大{current_bar['tick_volume']/avg_volume:.1f}倍)"
                )
                return True, info
                
            return False, "突破不满足条件"
            
        except Exception as e:
            logger.error(f"盘整突破分析出错: {str(e)}")
            return False, f"分析出错: {str(e)}"
            
    def analyze_false_breakout(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        假突破回踩分析
        
        分析价格突破关键水平后迅速回踩的假突破行为，这通常是反向交易的机会。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'open', 'high', 'low', 'close'
        
        返回:
            Tuple[bool, str]: (是否形成假突破回踩, 详细信息)
        
        分析逻辑:
        1. 识别前期关键水平(支撑或阻力)
        2. 检测是否有短暂突破后的回踩
        3. 确认回踩的力度和速度
        """
        try:
            if len(data) < 15:
                return False, "数据量不足"
                
            # 使用最近的数据进行分析
            recent_data = data.tail(15).copy()
            
            # 识别前期关键水平(使用前10根K线)
            base_data = recent_data.iloc[:-5]
            resistance_level = base_data['high'].max()
            support_level = base_data['low'].min()
            
            # 获取最近5根K线
            last_bars = recent_data.iloc[-5:]
            
            # 检测假突破上行回踩
            false_breakout_up = False
            false_breakout_down = False
            
            # 上行假突破: 某根K线高点突破阻力位，但收盘价回落到阻力位下方
            for i in range(len(last_bars) - 1):
                bar = last_bars.iloc[i]
                next_bar = last_bars.iloc[i+1]
                
                if (bar['high'] > resistance_level and 
                    bar['close'] < resistance_level and 
                    next_bar['open'] < resistance_level):
                    false_breakout_up = True
                    break
            
            # 下行假突破: 某根K线低点突破支撑位，但收盘价回升到支撑位上方
            for i in range(len(last_bars) - 1):
                bar = last_bars.iloc[i]
                next_bar = last_bars.iloc[i+1]
                
                if (bar['low'] < support_level and 
                    bar['close'] > support_level and 
                    next_bar['open'] > support_level):
                    false_breakout_down = True
                    break
            
            # 获取最新K线
            current_bar = recent_data.iloc[-1]
            
            # 判断是否形成有效的假突破回踩
            if false_breakout_up:
                direction = "上行"
                level = resistance_level
                reversal_strength = (resistance_level - current_bar['close']) / resistance_level
            elif false_breakout_down:
                direction = "下行"
                level = support_level
                reversal_strength = (current_bar['close'] - support_level) / support_level
            else:
                return False, "未检测到假突破回踩"
            
            # 判断回踩力度是否足够
            valid_reversal = reversal_strength > self.price_threshold
            
            if valid_reversal:
                info = (
                    f"{direction}假突破回踩 - "
                    f"关键水平: {level:.6f}, "
                    f"回踩力度: {reversal_strength*100:.2f}%, "
                    f"当前价格: {current_bar['close']:.6f}"
                )
                return True, info
                
            return False, "回踩力度不足"
            
        except Exception as e:
            logger.error(f"假突破回踩分析出错: {str(e)}")
            return False, f"分析出错: {str(e)}"
            
    def analyze_intraday_trend_continuation(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        日内趋势延续分析
        
        分析日内已形成趋势的延续机会，特别是回调后再次向趋势方向运行的情况。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'open', 'high', 'low', 'close'
        
        返回:
            Tuple[bool, str]: (是否形成趋势延续信号, 详细信息)
        
        分析逻辑:
        1. 确定日内趋势方向
        2. 识别趋势中的回调
        3. 确认回调结束并再次向趋势方向运行
        """
        try:
            if len(data) < 20:
                return False, "数据量不足"
                
            # 使用最近的数据进行分析
            recent_data = data.tail(20).copy()
            
            # 计算简单的移动平均线来确定趋势
            recent_data['ma5'] = recent_data['close'].rolling(5).mean()
            recent_data['ma10'] = recent_data['close'].rolling(10).mean()
            
            # 去除NaN值
            valid_data = recent_data.dropna()
            if len(valid_data) < 10:
                return False, "有效数据不足"
            
            # 确定趋势方向
            uptrend = valid_data['ma5'].iloc[-2] > valid_data['ma10'].iloc[-2]
            downtrend = valid_data['ma5'].iloc[-2] < valid_data['ma10'].iloc[-2]
            
            if not (uptrend or downtrend):
                return False, "无明确趋势"
            
            # 获取最近3根K线
            last_bars = valid_data.tail(3)
            
            # 检测回调结束信号
            if uptrend:
                # 上升趋势中的回调结束信号: 最近一根K线收阳且收盘价高于前一根K线高点
                pullback_end = (
                    last_bars['close'].iloc[-1] > last_bars['open'].iloc[-1] and
                    last_bars['close'].iloc[-1] > last_bars['high'].iloc[-2]
                )
                
                if pullback_end:
                    # 计算回调幅度
                    recent_high = valid_data['high'].iloc[:-3].max()
                    pullback_low = last_bars['low'].min()
                    pullback_size = (recent_high - pullback_low) / recent_high
                    
                    # 计算新的上涨动能
                    momentum = (last_bars['close'].iloc[-1] - last_bars['low'].iloc[-2]) / last_bars['low'].iloc[-2]
                    
                    info = (
                        f"上升趋势延续 - "
                        f"回调幅度: {pullback_size*100:.2f}%, "
                        f"新动能: {momentum*100:.2f}%, "
                        f"MA5/MA10: {valid_data['ma5'].iloc[-1]:.6f}/{valid_data['ma10'].iloc[-1]:.6f}"
                    )
                    return True, info
                    
            elif downtrend:
                # 下降趋势中的回调结束信号: 最近一根K线收阴且收盘价低于前一根K线低点
                pullback_end = (
                    last_bars['close'].iloc[-1] < last_bars['open'].iloc[-1] and
                    last_bars['close'].iloc[-1] < last_bars['low'].iloc[-2]
                )
                
                if pullback_end:
                    # 计算回调幅度
                    recent_low = valid_data['low'].iloc[:-3].min()
                    pullback_high = last_bars['high'].max()
                    pullback_size = (pullback_high - recent_low) / recent_low
                    
                    # 计算新的下跌动能
                    momentum = (last_bars['high'].iloc[-2] - last_bars['close'].iloc[-1]) / last_bars['high'].iloc[-2]
                    
                    info = (
                        f"下降趋势延续 - "
                        f"回调幅度: {pullback_size*100:.2f}%, "
                        f"新动能: {momentum*100:.2f}%, "
                        f"MA5/MA10: {valid_data['ma5'].iloc[-1]:.6f}/{valid_data['ma10'].iloc[-1]:.6f}"
                    )
                    return True, info
            
            return False, "未检测到趋势延续信号"
            
        except Exception as e:
            logger.error(f"日内趋势延续分析出错: {str(e)}")
            return False, f"分析出错: {str(e)}"
            
    def analyze_momentum_breakout(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        动量突破分析
        
        分析价格在短时间内出现的强势动量突破，通常伴随着成交量的显著放大。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame                必须包含的列：'open', 'high', 'low', 'close', 'tick_volume'
        
        返回:
            Tuple[bool, str]: (是否形成动量突破, 详细信息)
        
        分析逻辑:
        1. 检测价格突破前期阻力位
        2. 确认突破的力度和速度
        3. 验证成交量放大情况
        """
        try:
            if len(data) < 15:
                return False, "数据量不足"
                
            # 使用最近的数据进行分析
            recent_data = data.tail(15).copy()
            
            # 计算前期阻力位 - 使用前10根K线的最高价
            resistance = recent_data['high'].iloc[:-5].max()
            
            # 获取最新K线数据
            current_bar = recent_data.iloc[-1]
            prev_bar = recent_data.iloc[-2]
            
            # 计算突破幅度
            breakout_size = (current_bar['close'] - resistance) / resistance
            
            # 计算突破速度 - 使用K线实体大小
            momentum_speed = (current_bar['close'] - current_bar['open']) / current_bar['open']
            
            # 计算成交量放大情况
            avg_volume = recent_data['tick_volume'].iloc[:-1].mean()
            volume_surge = current_bar['tick_volume'] / avg_volume
            
            # 判断是否形成有效的动量突破
            is_valid = (
                current_bar['close'] > resistance * (1 + self.price_threshold) and  # 有效突破前期阻力
                breakout_size > self.price_threshold and  # 突破幅度足够
                momentum_speed > self.price_threshold * 0.5 and  # 动能足够
                volume_surge > self.volume_threshold  # 成交量放大
            )
            
            if is_valid:
                info = (
                    f"突破阻力位: {resistance:.6f}, "
                    f"突破幅度: {breakout_size*100:.2f}%, "
                    f"动能速度: {momentum_speed*100:.2f}%, "
                    f"成交量放大: {volume_surge:.1f}倍"
                )
                return True, info
                
            return False, "未形成有效的动量突破"
            
        except Exception as e:
            logger.error(f"动量突破分析出错: {str(e)}")
            return False, f"分析出错: {str(e)}"
            
    def analyze_key_level_reversal(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        关键价位反转分析
        
        分析价格在关键支撑位或阻力位附近的反转信号。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'open', 'high', 'low', 'close', 'tick_volume'
        
        返回:
            Tuple[bool, str]: (是否形成关键价位反转, 详细信息)
        
        分析逻辑:
        1. 识别关键支撑或阻力位
        2. 检测价格在关键位附近的反转K线形态
        3. 确认反转的成交量特征
        """
        try:
            if len(data) < 30:
                return False, "数据量不足"
                
            # 使用最近的数据进行分析
            recent_data = data.tail(30).copy()
            
            # 识别关键支撑位和阻力位
            support_level = recent_data['low'].iloc[:-5].min()
            resistance_level = recent_data['high'].iloc[:-5].max()
            
            # 获取最新K线数据
            current_bar = recent_data.iloc[-1]
            prev_bar = recent_data.iloc[-2]
            
            # 计算价格与关键位的距离
            distance_to_support = abs(current_bar['low'] - support_level) / support_level
            distance_to_resistance = abs(current_bar['high'] - resistance_level) / resistance_level
            
            # 判断是否接近关键位
            near_key_level = min(distance_to_support, distance_to_resistance) < self.price_threshold * 2
            
            if not near_key_level:
                return False, "未接近关键价位"
            
            # 判断K线形态是否为反转形态
            # 1. 下探支撑位后收阳 - 看多反转
            bullish_reversal = (
                distance_to_support < self.price_threshold * 2 and
                current_bar['close'] > current_bar['open'] and
                current_bar['close'] > prev_bar['close'] and
                current_bar['low'] < prev_bar['low']
            )
            
            # 2. 上探阻力位后收阴 - 看空反转
            bearish_reversal = (
                distance_to_resistance < self.price_threshold * 2 and
                current_bar['close'] < current_bar['open'] and
                current_bar['close'] < prev_bar['close'] and
                current_bar['high'] > prev_bar['high']
            )
            
            # 计算成交量特征
            avg_volume = recent_data['tick_volume'].iloc[:-1].mean()
            volume_surge = current_bar['tick_volume'] / avg_volume
            
            # 判断是否形成有效的反转信号
            is_valid = (bullish_reversal or bearish_reversal) and volume_surge > 1.2
            
            if is_valid:
                if bullish_reversal:
                    info = (
                        f"看多反转 - "
                        f"支撑位: {support_level:.6f}, "
                        f"距离支撑: {distance_to_support*100:.2f}%, "
                        f"成交量: {volume_surge:.1f}倍"
                    )
                else:
                    info = (
                        f"看空反转 - "
                        f"阻力位: {resistance_level:.6f}, "
                        f"距离阻力: {distance_to_resistance*100:.2f}%, "
                        f"成交量: {volume_surge:.1f}倍"
                    )
                return True, info
                
            return False, "未形成有效的关键价位反转"
            
        except Exception as e:
            logger.error(f"关键价位反转分析出错: {str(e)}")
            return False, f"分析出错: {str(e)}"
            
    def analyze_high_low_breakout(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        高低点突破分析
        
        分析价格突破前期高点或低点的情况，这通常是趋势延续或转折的信号。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'high', 'low', 'close', 'tick_volume'
        
        返回:
            Tuple[bool, str]: (是否形成高低点突破, 详细信息)
        
        分析逻辑:
        1. 识别前期高点或低点
        2. 检测价格是否有效突破
        3. 确认突破的成交量和动能
        """
        try:
            if len(data) < 20:
                return False, "数据量不足"
                
            # 使用最近的数据进行分析
            recent_data = data.tail(20).copy()
            
            # 识别前期高点和低点 - 使用前15根K线
            previous_data = recent_data.iloc[:-5]
            previous_high = previous_data['high'].max()
            previous_low = previous_data['low'].min()
            
            # 获取最新K线数据
            current_bar = recent_data.iloc[-1]
            
            # 计算突破幅度
            upside_breakout = (current_bar['close'] - previous_high) / previous_high
            downside_breakout = (previous_low - current_bar['close']) / previous_low
            
            # 计算成交量放大情况
            avg_volume = recent_data['tick_volume'].iloc[:-1].mean()
            volume_surge = current_bar['tick_volume'] / avg_volume
            
            # 判断是否形成有效的高点突破
            is_upside_breakout = (
                upside_breakout > self.price_threshold and
                current_bar['close'] > previous_high and
                volume_surge > self.volume_threshold
            )
            
            # 判断是否形成有效的低点突破
            is_downside_breakout = (
                downside_breakout > self.price_threshold and
                current_bar['close'] < previous_low and
                volume_surge > self.volume_threshold
            )
            
            if is_upside_breakout:
                info = (
                    f"向上突破 - "
                    f"前期高点: {previous_high:.6f}, "
                    f"突破幅度: {upside_breakout*100:.2f}%, "
                    f"成交量: {volume_surge:.1f}倍"
                )
                return True, info
                
            elif is_downside_breakout:
                info = (
                    f"向下突破 - "
                    f"前期低点: {previous_low:.6f}, "
                    f"突破幅度: {downside_breakout*100:.2f}%, "
                    f"成交量: {volume_surge:.1f}倍"
                )
                return True, info
                
            return False, "未形成有效的高低点突破"
            
        except Exception as e:
            logger.error(f"高低点突破分析出错: {str(e)}")
            return False, f"分析出错: {str(e)}"
            
    def analyze_volume_price_divergence(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        成交量价格背离分析
        
        分析成交量与价格走势的背离情况，这通常是趋势转折的信号。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'close', 'tick_volume'
        
        返回:
            Tuple[bool, str]: (是否形成成交量价格背离, 详细信息)
        
        分析逻辑:
        1. 比较价格趋势与成交量趋势
        2. 检测背离情况
        3. 确认背离的持续性和强度
        """
        try:
            if len(data) < 15:
                return False, "数据量不足"
                
            # 使用最近的数据进行分析
            recent_data = data.tail(15).copy()
            
            # 计算价格和成交量的变化趋势
            price_changes = []
            volume_changes = []
            
            for i in range(1, len(recent_data)):
                price_change = recent_data['close'].iloc[i] - recent_data['close'].iloc[i-1]
                volume_change = float(recent_data['tick_volume'].iloc[i]) - float(recent_data['tick_volume'].iloc[i-1])                
                price_changes.append(price_change)
                volume_changes.append(volume_change)
            
            # 计算最近5根K线的价格和成交量趋势
            recent_price_trend = sum(price_changes[-5:])
            recent_volume_trend = sum(volume_changes[-5:])
            
            # 判断是否形成背离
            bullish_divergence = recent_price_trend < 0 and recent_volume_trend > 0
            bearish_divergence = recent_price_trend > 0 and recent_volume_trend < 0
            
            # 计算背离强度
            if bullish_divergence or bearish_divergence:
                price_strength = abs(recent_price_trend) / recent_data['close'].iloc[-6]
                volume_strength = abs(recent_volume_trend) / recent_data['tick_volume'].iloc[-6]
                divergence_strength = min(price_strength, volume_strength)
                
                # 判断背离是否足够强
                is_strong_divergence = divergence_strength > self.price_threshold
                
                if is_strong_divergence:
                    if bullish_divergence:
                        info = (
                            f"看多背离 - "
                            f"价格趋势: {recent_price_trend:.6f}, "
                            f"成交量趋势: {recent_volume_trend:.1f}, "
                            f"背离强度: {divergence_strength*100:.2f}%"
                        )
                    else:
                        info = (
                            f"看空背离 - "
                            f"价格趋势: {recent_price_trend:.6f}, "
                            f"成交量趋势: {recent_volume_trend:.1f}, "
                            f"背离强度: {divergence_strength*100:.2f}%"
                        )
                    return True, info
            
            return False, "未形成有效的成交量价格背离"
            
        except Exception as e:
            logger.error(f"成交量价格背离分析出错: {str(e)}")
            return False, f"分析出错: {str(e)}"
            
    def analyze_intraday_support_resistance(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        日内支撑阻力测试分析
        
        分析价格对日内形成的支撑位或阻力位的测试情况。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'high', 'low', 'close', 'open'
        
        返回:
            Tuple[bool, str]: (是否形成支撑阻力测试, 详细信息)
        
        分析逻辑:
        1. 识别日内形成的支撑位和阻力位
        2. 检测价格对这些位置的测试情况
        3. 确认测试后的反应
        """
        try:
            if len(data) < 20:
                return False, "数据量不足"
                
            # 使用最近的数据进行分析
            recent_data = data.tail(20).copy()
            
            # 识别日内支撑位和阻力位 - 使用前15根K线
            previous_data = recent_data.iloc[:-5]
            resistance_level = previous_data['high'].max()
            support_level = previous_data['low'].min()
            
            # 获取最新K线数据
            current_bar = recent_data.iloc[-1]
            
            # 计算价格与支撑阻力位的距离
            distance_to_support = abs(current_bar['low'] - support_level) / support_level
            distance_to_resistance = abs(current_bar['high'] - resistance_level) / resistance_level
            
            # 判断是否接近支撑阻力位
            near_support = distance_to_support < self.price_threshold * 2
            near_resistance = distance_to_resistance < self.price_threshold * 2
            
            # 判断测试结果
            support_test = near_support and current_bar['close'] > current_bar['open'] and current_bar['close'] > support_level
            resistance_test = near_resistance and current_bar['close'] < current_bar['open'] and current_bar['close'] < resistance_level
            
            if support_test:
                info = (
                    f"支撑位测试 - "
                    f"支撑位: {support_level:.6f}, "
                    f"距离支撑: {distance_to_support*100:.2f}%, "
                    f"反弹力度: {(current_bar['close']/current_bar['low'] - 1)*100:.2f}%"
                )
                return True, info
                
            elif resistance_test:
                info = (
                    f"阻力位测试 - "
                    f"阻力位: {resistance_level:.6f}, "
                    f"距离阻力: {distance_to_resistance*100:.2f}%, "
                    f"回落力度: {(current_bar['high']/current_bar['close'] - 1)*100:.2f}%"
                )
                return True, info
                
            return False, "未形成有效的支撑阻力测试"
            
        except Exception as e:
            logger.error(f"日内支撑阻力测试分析出错: {str(e)}")
            return False, f"分析出错: {str(e)}"
            
    # 修复缩进，将这个方法移到类级别
    def analyze_volatility_contraction_expansion(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        波动率收缩扩张分析
        
        识别波动率先收缩后扩张的形态，这通常是趋势变化的信号
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
        
        返回:
            Tuple[bool, str]: (是否形成有效形态, 详细信息)
        """
        try:
            if len(data) < 20:
                return False, ""
                
            # 计算最近20根K线的波动率(高低点差值)
            recent_data = data.tail(20).copy()
            recent_data['volatility'] = recent_data['high'] - recent_data['low']
            
            # 计算波动率的移动平均
            recent_data['vol_ma5'] = recent_data['volatility'].rolling(window=5).mean()
            
            # 如果数据不足，返回
            if recent_data['vol_ma5'].isna().any():
                return False, ""
                
            # 检查波动率收缩后扩张
            vol_values = recent_data['vol_ma5'].dropna().values
            
            # 至少需要10个有效值
            if len(vol_values) < 10:
                return False, ""
                
            # 检查前半段是否收缩
            first_half = vol_values[:-5]
            is_contracting = all(first_half[i] >= first_half[i+1] for i in range(len(first_half)-1))
            
            # 检查后半段是否扩张
            second_half = vol_values[-5:]
            is_expanding = all(second_half[i] <= second_half[i+1] for i in range(len(second_half)-1))
            
            # 计算收缩和扩张的幅度
            if is_contracting and is_expanding:
                contraction_ratio = first_half[0] / first_half[-1] if first_half[-1] > 0 else 0
                expansion_ratio = second_half[-1] / second_half[0] if second_half[0] > 0 else 0
                
                # 判断是否有效
                is_valid = (contraction_ratio > 1.3 and expansion_ratio > 1.3)
                
                # 获取当前价格和方向
                current_price = data['close'].iloc[-1]
                direction = "看涨" if data['close'].iloc[-1] > data['open'].iloc[-1] else "看跌"
                
                info = (
                    f"波动率收缩扩张 - {direction}, "
                    f"收缩比: {contraction_ratio:.2f}, "
                    f"扩张比: {expansion_ratio:.2f}, "
                    f"当前价格: {current_price:.6f}"
                )
                
                return is_valid, info
                
            return False, ""
            
        except Exception as e:
            logger.error(f"波动率收缩扩张分析出错: {str(e)}")
            return False, ""

    # 修复缩进，将这个方法也移到类级别
    def analyze_gap_fill(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        开盘缺口填补分析
        
        识别开盘缺口并判断是否已填补
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
        
        返回:
            Tuple[bool, str]: (是否形成有效形态, 详细信息)
        """
        try:
            if len(data) < 3:
                return False, ""
                
            # 获取最近3根K线
            recent_data = data.tail(3).copy()
            
            # 检查是否存在缺口
            prev_close = recent_data['close'].iloc[-3]
            gap_open = recent_data['open'].iloc[-2]
            
            # 计算缺口大小
            gap_size = abs(gap_open - prev_close)
            gap_percent = gap_size / prev_close * 100
            
            # 如果缺口太小，不考虑
            if gap_percent < 0.1:  # 小于0.1%的缺口忽略
                return False, ""
                
            # 判断缺口方向
            gap_direction = "上涨" if gap_open > prev_close else "下跌"
            
            # 检查是否已填补
            current_price = recent_data['close'].iloc[-1]
            
            is_filled = False
            if gap_direction == "上涨" and current_price <= prev_close:
                is_filled = True
            elif gap_direction == "下跌" and current_price >= prev_close:
                is_filled = True
                
            if is_filled:
                info = (
                    f"缺口填补 - {gap_direction}缺口, "
                    f"缺口大小: {gap_percent:.2f}%, "
                    f"缺口价位: {prev_close:.6f} -> {gap_open:.6f}, "
                    f"当前价格: {current_price:.6f}"
                )
                return True, info
                
            return False, ""
            
        except Exception as e:
            logger.error(f"开盘缺口填补分析出错: {str(e)}")
            return False, ""
    
    @timeit
    def analyze_all(self, data: pd.DataFrame) -> Dict[str, Tuple[bool, str]]:
        """
        执行所有价格行为分析
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
        
        返回:
            Dict[str, Tuple[bool, str]]: 分析结果字典
                键: 分析方法名称
                值: (是否形成有效形态, 详细信息)
        """
        results = {}
        
        # 执行所有分析方法
        results['consolidation_breakout'] = self.analyze_consolidation_breakout(data)
        results['false_breakout'] = self.analyze_false_breakout(data)
        results['intraday_trend_continuation'] = self.analyze_intraday_trend_continuation(data)
        results['momentum_breakout'] = self.analyze_momentum_breakout(data)
        results['key_level_reversal'] = self.analyze_key_level_reversal(data)
        results['high_low_breakout'] = self.analyze_high_low_breakout(data)
        results['volume_price_divergence'] = self.analyze_volume_price_divergence(data)
        results['intraday_support_resistance'] = self.analyze_intraday_support_resistance(data)
        results['volatility_contraction_expansion'] = self.analyze_volatility_contraction_expansion(data)
        results['gap_fill'] = self.analyze_gap_fill(data)
        
        return results
    
    def get_consolidated_signals(self, results: Dict[str, Tuple[bool, str]], symbol: str, timeframe: str) -> Tuple[bool, str]:
        """
        整合所有价格行为分析信号
        
        参数:
            results (Dict[str, Tuple[bool, str]]): 分析结果字典
            symbol (str): 交易品种
            timeframe (str): 时间周期
            
        返回:
            Tuple[bool, str]: (是否有信号, 整合后的信号信息)
        """
        # 筛选出有效的信号
        valid_signals = {k: v[1] for k, v in results.items() if v[0]}
        
        if not valid_signals:
            return False, ""
            
        # 构建整合信号
        signal_count = len(valid_signals)
        signal_strength = "强" if signal_count >= 3 else "中" if signal_count >= 2 else "弱"
        
        # 构建信号标题
        header = f"【{symbol} {timeframe}】发现{signal_count}个价格行为信号 - {signal_strength}信号"
        
        # 构建信号详情
        details = []
        for signal_type, info in valid_signals.items():
            # 将信号类型转换为中文名称
            signal_name = self._get_signal_name(signal_type)
            details.append(f"- {signal_name}: {info}")
            
        # 组合完整信息
        consolidated_info = header + "\n" + "\n".join(details)
        
        return True, consolidated_info
    
    def _get_signal_name(self, signal_type: str) -> str:
        """
        将信号类型转换为中文名称
        
        参数:
            signal_type (str): 信号类型
            
        返回:
            str: 中文名称
        """
        signal_names = {
            'consolidation_breakout': '盘整突破',
            'false_breakout': '假突破回踩',
            'intraday_trend_continuation': '趋势延续',
            'momentum_breakout': '动量突破',
            'key_level_reversal': '关键价位反转',
            'high_low_breakout': '高低点突破',
            'volume_price_divergence': '成交量价格背离',
            'intraday_support_resistance': '支撑阻力测试',
            'volatility_contraction_expansion': '波动率收缩扩张',
            'gap_fill': '缺口填补'
        }
        
        return signal_names.get(signal_type, signal_type)