//+------------------------------------------------------------------+
//|                                    ProbabilityBreakout_v4_MLFeatures.mq5 |
//|                    客观化多维度特征学习系统 - EMA20核心版              |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "概率交易系统 v4.0 - 客观化特征学习版"
#property link      ""
#property version   "4.00"

//--- 输入参数
input group "=== 学习系统参数 ==="
input int      InpInitialLearningDays = 60;      // 初始学习天数
input int      InpIncrementalDays = 7;           // 增量学习天数
input bool     InpForceRebuild = false;          // 强制重建数据库
input int      InpMinSampleSize = 50;            // 最小样本数量要求

input group "=== 概率计算参数 ==="
input int      InpSuccessConfirmBars = 3;        // 成功确认需要的K线数
input double   InpProbThreshold = 0.70;          // 概率阈值
input bool     InpUseAdvancedProbability = true; // 使用高级概率计算

input group "=== EMA突破系统 (完全客观) ==="
input int      InpEmaPeriod = 20;                // EMA周期 (客观基准)
input double   InpEmaTolerancePips = 2;          // EMA突破容差（点）

input group "=== 特征工程参数 ==="
input bool     InpEnableTimeFeatures = true;     // 启用时间特征
input bool     InpEnableObjectiveFeatures = true; // 启用客观技术特征
input bool     InpEnableAdvancedPriceAction = true; // 启用进阶价格行为

input group "=== 风险管理 ==="
input double   InpRiskPercent = 1.0;             // 每笔交易风险百分比
input double   InpRiskRewardRatio = 2.0;         // 风险回报比
input int      InpMaxConsecutiveLoss = 3;        // 最大连续亏损次数

//--- 枚举定义
enum ENUM_SESSION_TYPE
{
    SESSION_ASIAN = 0,      // 亚洲时段
    SESSION_EUROPEAN = 1,   // 欧洲时段  
    SESSION_AMERICAN = 2    // 美洲时段
};

//--- 客观化多维度特征数据结构
struct ObjectiveBreakoutEvent
{
    // === 基础信息 ===
    datetime time;              // 突破时间
    double   emaLevel;          // EMA20水平（客观基准）
    bool     isUpBreakout;      // 是否向上突破EMA20
    bool     wasSuccessful;     // 是否成功
    int      maintainBars;      // 维持的K线数
    
    // === 时间特征（完全客观） ===
    int      sessionType;       // 交易时段类型
    int      hourOfDay;         // 小时(0-23)
    int      dayOfWeek;         // 星期几(1-7)
    
    // === 客观技术特征 ===
    double   rsi14;             // 14期RSI（客观）
    double   atr14_normalized;  // 标准化ATR（客观波动率）
    double   emaDeviation;      // 价格偏离EMA的标准差倍数
    
    // === 进阶价格行为特征（完全客观） ===
    double   priceAcceleration; // 价格加速度（二阶导数）
    double   volumePriceRatio;  // 成交量价格关系
    double   momentumShift;     // 实时动量偏移
    double   multiTFConsistency; // 多周期一致性评分
    double   volatilityBurst;   // 波动率突变指标
    
    // === 成功度量 ===
    double   actualReturn;      // 实际收益率
    int      maxDrawdownBars;   // 最大回撤K线数
    double   riskAdjustedReturn; // 风险调整收益
};

//--- 特征权重结构
struct ObjectiveFeatureWeights
{
    double timeWeight;          // 时间特征权重
    double objectiveWeight;     // 客观技术特征权重
    double priceActionWeight;   // 进阶价格行为权重
    
    // === Phase 2: 细分权重 ===
    double rsiWeight;           // RSI权重
    double emaDeviationWeight;  // EMA偏离度权重
    double accelerationWeight;  // 价格加速度权重
    double momentumWeight;      // 动量偏移权重
    double volumePriceWeight;   // 成交量价格关系权重
    double multiTFWeight;       // 多周期一致性权重
    double volatilityWeight;    // 波动率突变权重
    
    datetime lastUpdate;        // 最后更新时间
    int sampleSize;            // 样本数量
    double accuracy;           // 预测准确率
};

//--- 数据库头结构
struct DatabaseHeader
{
    int      version;           // 数据库版本 (v4.0 客观版 = 5)
    datetime lastUpdate;        // 最后更新时间
    int      totalEvents;       // 总事件数
    char     symbol[12];        // 交易品种 (改为固定大小字符数组)
    int      timeframe;         // 时间周期
    bool     isObjectiveVersion; // 是否为客观化版本
};

//--- 全局变量
ObjectiveBreakoutEvent historyEvents[];
DatabaseHeader dbHeader;
ObjectiveFeatureWeights featureWeights;
string databaseFile;
int consecutiveLoss = 0;
bool tradingEnabled = true;
datetime lastDataUpdate = 0;

// 客观指标句柄
int rsiHandle, atrHandle, emaHandle;
int emaH4Handle;  // H4周期EMA用于多周期分析

// === Phase 2: 高级概率计算系统新增变量 ===
struct FeatureNormalizationParams
{
    // RSI标准化参数
    double rsiMin, rsiMax;
    
    // EMA偏离度标准化参数
    double emaDeviationMin, emaDeviationMax;
    
    // 价格加速度标准化参数
    double accelerationMin, accelerationMax;
    
    // 动量偏移标准化参数
    double momentumMin, momentumMax;
    
    // 成交量价格比标准化参数
    double volumePriceRatioMin, volumePriceRatioMax;
    
    // 波动率突变标准化参数
    double volatilityBurstMin, volatilityBurstMax;
    
    datetime lastCalculated;
    int sampleSize;
};

// === Phase 2: 时间特征成功率统计 ===
struct TimeFeatureStats
{
    double hourSuccessRate[24];        // 每小时成功率
    double sessionSuccessRate[3];      // 时段成功率
    double dayOfWeekSuccessRate[7];    // 星期成功率
    int hourSampleCount[24];           // 每小时样本数
    int sessionSampleCount[3];         // 时段样本数
    int dayOfWeekSampleCount[7];       // 星期样本数
    bool isCalculated;                 // 是否已计算
};

// === Phase 2: 实时交易统计 ===
struct RealTimeTradeStats
{
    int totalTrades;                   // 总交易数
    int successfulTrades;              // 成功交易数
    double totalReturn;                // 总收益
    double probabilitySum;             // 概率累计和
    double accuracyByProbRange[10];    // 按概率区间的准确性
    int countByProbRange[10];          // 按概率区间的交易数
    datetime lastUpdate;               // 最后更新时间
};

FeatureNormalizationParams normParams;
TimeFeatureStats timeStats;
RealTimeTradeStats tradeStats;
double lastProbabilityScore = 0.0;
bool isLearningPhase = true;  // 初始学习阶段

//+------------------------------------------------------------------+
//| 专家顾问初始化函数                                                  |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== 概率突破系统 v4.0 初始化 - 客观化特征学习版 ===");
    
    // 初始化客观技术指标
    if(!InitializeObjectiveIndicators())
    {
        Print("客观技术指标初始化失败！");
        return INIT_FAILED;
    }
    
    // 初始化特征权重
    InitializeObjectiveFeatureWeights();
    
    // 初始化特征标准化参数
    InitializeFeatureNormalization();
    
    // 构建数据库文件名
    databaseFile = StringFormat("%s_%s_%d_ObjectiveMLDB_v4.bin", 
                                _Symbol, 
                                EnumToString((ENUM_TIMEFRAMES)_Period), 
                                InpEmaPeriod);
    
    Print("客观化数据库文件: ", databaseFile);
    
    // 初始化学习系统
    if(!InitializeObjectiveLearningSystem())
    {
        Print("客观化学习系统初始化失败！");
        return INIT_FAILED;
    }
    
    Print("客观化学习系统初始化完成，历史事件: ", ArraySize(historyEvents), " 个");
    PrintObjectiveStatistics();
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| 初始化客观技术指标                                                 |
//+------------------------------------------------------------------+
bool InitializeObjectiveIndicators()
{
    // 只使用完全客观的指标
    rsiHandle = iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE);
    atrHandle = iATR(_Symbol, PERIOD_CURRENT, 14);
    emaHandle = iMA(_Symbol, PERIOD_CURRENT, InpEmaPeriod, 0, MODE_EMA, PRICE_CLOSE);
    
    // 多周期EMA（H4用于一致性分析）
    emaH4Handle = iMA(_Symbol, PERIOD_H4, InpEmaPeriod, 0, MODE_EMA, PRICE_CLOSE);
    
    if(rsiHandle == INVALID_HANDLE || atrHandle == INVALID_HANDLE || 
       emaHandle == INVALID_HANDLE || emaH4Handle == INVALID_HANDLE)
    {
        Print("客观指标句柄创建失败");
        return false;
    }
    
    Print("客观技术指标初始化成功 - RSI, ATR, EMA20");
    return true;
}

//+------------------------------------------------------------------+
//| 初始化特征标准化参数                                               |
//+------------------------------------------------------------------+
void InitializeFeatureNormalization()
{
    // 设置初始范围值（将在数据分析后动态调整）
    normParams.rsiMin = 0.0;
    normParams.rsiMax = 100.0;
    
    normParams.emaDeviationMin = 0.0;
    normParams.emaDeviationMax = 5.0;
    
    normParams.accelerationMin = -2.0;
    normParams.accelerationMax = 2.0;
    
    normParams.momentumMin = -3.0;
    normParams.momentumMax = 3.0;
    
    normParams.volumePriceRatioMin = 0.1;
    normParams.volumePriceRatioMax = 5.0;
    
    normParams.volatilityBurstMin = 0.3;
    normParams.volatilityBurstMax = 3.0;
    
    normParams.lastCalculated = 0;
    normParams.sampleSize = 0;
}

//+------------------------------------------------------------------+
//| 初始化客观特征权重                                                 |
//+------------------------------------------------------------------+
void InitializeObjectiveFeatureWeights()
{
    featureWeights.timeWeight = 0.25;          // 时间特征权重25%
    featureWeights.objectiveWeight = 0.35;     // 客观技术特征权重35%
    featureWeights.priceActionWeight = 0.40;   // 进阶价格行为权重40%
    
    // === Phase 2: 细分权重初始化 ===
    featureWeights.rsiWeight = 0.5;            // RSI权重 (客观技术特征内部)
    featureWeights.emaDeviationWeight = 0.5;   // EMA偏离度权重
    featureWeights.accelerationWeight = 0.2;   // 价格加速度权重 (进阶价格行为内部)
    featureWeights.momentumWeight = 0.2;       // 动量偏移权重
    featureWeights.volumePriceWeight = 0.2;    // 成交量价格关系权重
    featureWeights.multiTFWeight = 0.2;        // 多周期一致性权重
    featureWeights.volatilityWeight = 0.2;     // 波动率突变权重
    
    featureWeights.lastUpdate = TimeCurrent();
    featureWeights.sampleSize = 0;
    featureWeights.accuracy = 0.0;
    
    Print("客观特征权重初始化完成");
}

//+------------------------------------------------------------------+
//| 初始化客观学习系统                                                 |
//+------------------------------------------------------------------+
bool InitializeObjectiveLearningSystem()
{
    bool needRebuild = InpForceRebuild;
    
    // 尝试加载现有客观化数据库
    if(!needRebuild && LoadObjectiveDatabase())
    {
        Print("成功加载现有客观化数据库，最后更新: ", TimeToString(dbHeader.lastUpdate));
        
        // 检查是否需要增量更新
        datetime currentTime = TimeCurrent();
        int daysSinceUpdate = (int)((currentTime - dbHeader.lastUpdate) / 86400);
        
        if(daysSinceUpdate >= InpIncrementalDays)
        {
            Print("数据库需要更新，距离上次更新: ", daysSinceUpdate, " 天");
            if(!IncrementalUpdateObjective(InpIncrementalDays + 5))
            {
                Print("增量更新失败，执行完整重建");
                needRebuild = true;
            }
        }
    }
    else
    {
        needRebuild = true;
    }
    
    // 需要重建数据库
    if(needRebuild)
    {
        Print("构建新的客观化特征数据库...");
        if(!BuildObjectiveDatabase())
        {
            Print("客观化数据库构建失败");
            return false;
        }
        
        if(!SaveObjectiveDatabase())
        {
            Print("客观化数据库保存失败");
            return false;
        }
    }
    
    // === Phase 2: 计算特征标准化参数 ===
    if(!CalculateFeatureNormalizationParams())
    {
        Print("特征标准化参数计算失败，将使用默认值");
        isLearningPhase = true;
    }
    else
    {
        // === Phase 2: 计算时间特征统计 ===
        if(!CalculateTimeFeatureStats())
        {
            Print("时间特征统计计算失败，将使用默认值");
            isLearningPhase = true;
        }
        else
        {
            isLearningPhase = false;  // 所有准备工作完成，可以进入交易阶段
            Print("智能概率计算系统准备就绪！");
            Print("特征标准化参数: 已计算，样本数量 = ", normParams.sampleSize);
            Print("时间特征统计: 已计算，可用于概率评分");
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 构建客观化特征数据库                                               |
//+------------------------------------------------------------------+
bool BuildObjectiveDatabase()
{
    // 计算需要的历史数据量
    int totalBars = InpInitialLearningDays * 24;  // 假设H1图表
    if(_Period == PERIOD_M15) totalBars = InpInitialLearningDays * 96;
    else if(_Period == PERIOD_M5) totalBars = InpInitialLearningDays * 288;
    else if(_Period == PERIOD_H4) totalBars = InpInitialLearningDays * 6;
    else if(_Period == PERIOD_D1) totalBars = InpInitialLearningDays;
    
    Print("准备分析 ", totalBars, " 根K线的客观化特征数据");
    
    return AnalyzeObjectiveHistoricalData(0, totalBars);
}

//+------------------------------------------------------------------+
//| 分析客观化历史数据                                                 |
//+------------------------------------------------------------------+
bool AnalyzeObjectiveHistoricalData(int startBar, int totalBars)
{
    // 获取历史价格数据
    double high[], low[], close[];
    long volume[];  // 修改为long类型以匹配CopyTickVolume
    datetime time[];
    
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(volume, true);
    ArraySetAsSeries(time, true);
    
    if(CopyHigh(_Symbol, PERIOD_CURRENT, startBar, totalBars, high) <= 0 ||
       CopyLow(_Symbol, PERIOD_CURRENT, startBar, totalBars, low) <= 0 ||
       CopyClose(_Symbol, PERIOD_CURRENT, startBar, totalBars, close) <= 0 ||
       CopyTickVolume(_Symbol, PERIOD_CURRENT, startBar, totalBars, volume) <= 0 ||
       CopyTime(_Symbol, PERIOD_CURRENT, startBar, totalBars, time) <= 0)
    {
        Print("历史数据获取失败");
        return false;
    }
    
    // 获取客观指标数据
    double rsiBuffer[], atrBuffer[], emaBuffer[], emaH4Buffer[];
    
    if(!GetObjectiveIndicatorData(totalBars, rsiBuffer, atrBuffer, emaBuffer, emaH4Buffer))
    {
        Print("客观指标数据获取失败");
        return false;
    }
    
    // 如果是增量更新，保持现有数据
    if(startBar == 0 && ArraySize(historyEvents) == 0)
    {
        ArrayResize(historyEvents, 0);
    }
    
    int eventsFound = 0;
    
    // 扫描历史数据，寻找EMA20突破事件并提取客观特征
    for(int i = InpEmaPeriod + InpSuccessConfirmBars; i < totalBars - 2; i++)
    {
        double tolerancePips = InpEmaTolerancePips * _Point;
        
        // 检查向上突破EMA20
        if(close[i] > emaBuffer[i] + tolerancePips && close[i+1] <= emaBuffer[i+1])
        {
            if(!IsDuplicateObjectiveEvent(time[i], true))
            {
                ObjectiveBreakoutEvent event;
                
                // 基础信息
                event.time = time[i];
                event.emaLevel = emaBuffer[i];
                event.isUpBreakout = true;
                event.wasSuccessful = CheckEMABreakoutSuccess(close, emaBuffer, i, true);
                event.maintainBars = CountEMAMaintainBars(close, emaBuffer, i, true);
                
                // 提取客观化特征
                ExtractObjectiveFeatures(event, i, time, high, low, close, volume,
                                        rsiBuffer, atrBuffer, emaBuffer, emaH4Buffer,
                                        true);
                
                AddObjectiveEvent(event);
                eventsFound++;
            }
        }
        
        // 检查向下突破EMA20
        if(close[i] < emaBuffer[i] - tolerancePips && close[i+1] >= emaBuffer[i+1])
        {
            if(!IsDuplicateObjectiveEvent(time[i], false))
            {
                ObjectiveBreakoutEvent event;
                
                // 基础信息
                event.time = time[i];
                event.emaLevel = emaBuffer[i];
                event.isUpBreakout = false;
                event.wasSuccessful = CheckEMABreakoutSuccess(close, emaBuffer, i, false);
                event.maintainBars = CountEMAMaintainBars(close, emaBuffer, i, false);
                
                // 提取客观化特征
                ExtractObjectiveFeatures(event, i, time, high, low, close, volume,
                                        rsiBuffer, atrBuffer, emaBuffer, emaH4Buffer,
                                        false);
                
                AddObjectiveEvent(event);
                eventsFound++;
            }
        }
        
        // 进度显示
        if(i % 100 == 0)
        {
            double progress = (double)i / totalBars * 100;
            Print("客观化特征分析进度: ", (int)progress, "%, 发现EMA突破事件: ", eventsFound);
        }
    }
    
    Print("客观化特征数据分析完成，新发现EMA突破事件: ", eventsFound, " 个");
    return true;
}

//+------------------------------------------------------------------+
//| 获取客观指标数据                                                   |
//+------------------------------------------------------------------+
bool GetObjectiveIndicatorData(int totalBars,
                               double &rsiBuffer[], double &atrBuffer[],
                               double &emaBuffer[], double &emaH4Buffer[])
{
    ArraySetAsSeries(rsiBuffer, true);
    ArraySetAsSeries(atrBuffer, true);
    ArraySetAsSeries(emaBuffer, true);
    ArraySetAsSeries(emaH4Buffer, true);
    
    if(CopyBuffer(rsiHandle, 0, 0, totalBars, rsiBuffer) <= 0 ||
       CopyBuffer(atrHandle, 0, 0, totalBars, atrBuffer) <= 0 ||
       CopyBuffer(emaHandle, 0, 0, totalBars, emaBuffer) <= 0 ||
       CopyBuffer(emaH4Handle, 0, 0, totalBars/4 + 10, emaH4Buffer) <= 0)
    {
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 提取客观化特征                                                     |
//+------------------------------------------------------------------+
void ExtractObjectiveFeatures(ObjectiveBreakoutEvent &event, int pos, 
                             const datetime &time[], const double &high[], const double &low[], 
                             const double &close[], const long &volume[],  // 修改为long类型
                             const double &rsiBuffer[], const double &atrBuffer[],
                             const double &emaBuffer[], const double &emaH4Buffer[],
                             bool isUpBreakout)
{
    // === 时间特征提取 ===
    if(InpEnableTimeFeatures)
    {
        ExtractObjectiveTimeFeatures(event, time[pos]);
    }
    
    // === 客观技术特征提取 ===
    if(InpEnableObjectiveFeatures)
    {
        ExtractObjectiveTechnicalFeatures(event, pos, close, rsiBuffer, atrBuffer, emaBuffer);
    }
    
    // === 进阶价格行为特征提取 ===
    if(InpEnableAdvancedPriceAction)
    {
        ExtractAdvancedPriceActionFeatures(event, pos, high, low, close, volume, 
                                         emaBuffer, emaH4Buffer, isUpBreakout);
    }
    
    // === 成功度量计算 ===
    CalculateObjectiveSuccessMetrics(event, pos, close, emaBuffer, isUpBreakout);
}

//+------------------------------------------------------------------+
//| 提取客观时间特征                                                   |
//+------------------------------------------------------------------+
void ExtractObjectiveTimeFeatures(ObjectiveBreakoutEvent &event, datetime eventTime)
{
    MqlDateTime timeStruct;
    TimeToStruct(eventTime, timeStruct);
    
    event.hourOfDay = timeStruct.hour;
    event.dayOfWeek = timeStruct.day_of_week;
    
    // 客观的时段划分（基于UTC时间）
    if(event.hourOfDay >= 0 && event.hourOfDay < 8)
        event.sessionType = SESSION_ASIAN;
    else if(event.hourOfDay >= 8 && event.hourOfDay < 16)
        event.sessionType = SESSION_EUROPEAN;
    else
        event.sessionType = SESSION_AMERICAN;
}

//+------------------------------------------------------------------+
//| 提取客观技术特征                                                   |
//+------------------------------------------------------------------+
void ExtractObjectiveTechnicalFeatures(ObjectiveBreakoutEvent &event, int pos, const double &close[],
                                      const double &rsiBuffer[], const double &atrBuffer[],
                                      const double &emaBuffer[])
{
    // RSI (完全客观)
    event.rsi14 = rsiBuffer[pos];
    
    // 标准化ATR (客观波动率度量)
    event.atr14_normalized = atrBuffer[pos] / close[pos] * 100;
    
    // EMA偏离度 (客观距离度量)
    double deviation = MathAbs(close[pos] - emaBuffer[pos]);
    event.emaDeviation = deviation / atrBuffer[pos]; // 以ATR标准化
}

//+------------------------------------------------------------------+
//| 提取进阶价格行为特征（完全客观）                                     |
//+------------------------------------------------------------------+
void ExtractAdvancedPriceActionFeatures(ObjectiveBreakoutEvent &event, int pos,
                                       const double &high[], const double &low[], const double &close[], 
                                       const long &volume[], const double &emaBuffer[],  // 修改为long类型
                                       const double &emaH4Buffer[], bool isUpBreakout)
{
    // 1. 价格加速度（二阶导数 - 完全客观）
    if(pos + 2 < ArraySize(close))
    {
        double velocity1 = close[pos] - close[pos+1];
        double velocity2 = close[pos+1] - close[pos+2];
        event.priceAcceleration = velocity1 - velocity2;
        
        // 标准化为ATR倍数
        double atr = CalculateSimpleATR(high, low, close, pos, 14);
        if(atr > 0)
            event.priceAcceleration /= atr;
    }
    else
    {
        event.priceAcceleration = 0.0;
    }
    
    // 2. 成交量价格关系（完全客观）
    double priceChange = MathAbs(close[pos] - close[pos+1]);
    if(priceChange > 0 && volume[pos] > 0)
    {
        event.volumePriceRatio = (double)volume[pos] / priceChange;  // 转换long为double
        // 标准化（使用过去10期的平均值）
        double avgVPR = 0;
        for(int i = pos+1; i <= pos+10 && i < ArraySize(volume); i++)
        {
            double pc = MathAbs(close[i] - close[i+1]);
            if(pc > 0 && volume[i] > 0)
                avgVPR += (double)volume[i] / pc;  // 转换long为double
        }
        avgVPR /= 10;
        if(avgVPR > 0)
            event.volumePriceRatio /= avgVPR;
    }
    else
    {
        event.volumePriceRatio = 1.0;
    }
    
    // 3. 实时动量偏移（完全客观）
    double atr = CalculateSimpleATR(high, low, close, pos, 14);
    if(atr > 0)
    {
        event.momentumShift = (close[pos] - emaBuffer[pos]) / atr;
    }
    else
    {
        event.momentumShift = 0.0;
    }
    
    // 4. 多周期一致性（完全客观）
    // 比较当前周期和H4周期的EMA关系
    int h4Index = pos / (PERIOD_H4 / _Period); // 转换为H4索引
    if(h4Index < ArraySize(emaH4Buffer))
    {
        bool currentBullish = close[pos] > emaBuffer[pos];
        bool h4Bullish = (h4Index > 0) ? emaH4Buffer[h4Index-1] > emaH4Buffer[h4Index] : currentBullish;
        event.multiTFConsistency = (currentBullish == h4Bullish) ? 1.0 : 0.0;
    }
    else
    {
        event.multiTFConsistency = 0.5; // 中性
    }
    
    // 5. 波动率突变指标（完全客观）
    double currentATR = CalculateSimpleATR(high, low, close, pos, 5);     // 短期ATR
    double longTermATR = CalculateSimpleATR(high, low, close, pos, 20);   // 长期ATR
    if(longTermATR > 0)
    {
        event.volatilityBurst = currentATR / longTermATR;
    }
    else
    {
        event.volatilityBurst = 1.0;
    }
}

//+------------------------------------------------------------------+
//| 计算客观成功度量                                                   |
//+------------------------------------------------------------------+
void CalculateObjectiveSuccessMetrics(ObjectiveBreakoutEvent &event, int pos, const double &close[],
                                     const double &emaBuffer[], bool isUpBreakout)
{
    double maxFavorable = 0.0;
    int maxDrawdownBars = 0;
    double totalRisk = 0.0;
    
    for(int i = pos - 1; i >= MathMax(0, pos - 10); i--)
    {
        double currentReturn;
        if(isUpBreakout)
        {
            currentReturn = (close[i] - event.emaLevel) / event.emaLevel;
            if(close[i] < emaBuffer[i]) maxDrawdownBars++;
        }
        else
        {
            currentReturn = (event.emaLevel - close[i]) / event.emaLevel;
            if(close[i] > emaBuffer[i]) maxDrawdownBars++;
        }
        
        if(currentReturn > maxFavorable)
            maxFavorable = currentReturn;
            
        // 累计风险（以偏离EMA的程度衡量）
        totalRisk += MathAbs(close[i] - emaBuffer[i]) / emaBuffer[i];
    }
    
    event.actualReturn = maxFavorable;
    event.maxDrawdownBars = maxDrawdownBars;
    
    // 风险调整收益（夏普比率思想）
    if(totalRisk > 0)
        event.riskAdjustedReturn = maxFavorable / (totalRisk / 10);
    else
        event.riskAdjustedReturn = maxFavorable;
}

//+------------------------------------------------------------------+
//| 检查EMA突破成功                                                    |
//+------------------------------------------------------------------+
bool CheckEMABreakoutSuccess(const double &close[], const double &emaBuffer[], int breakPos, bool isUp)
{
    int successCount = 0;
    
    for(int i = breakPos - 1; i >= breakPos - InpSuccessConfirmBars && i >= 0; i--)
    {
        if((isUp && close[i] > emaBuffer[i]) || (!isUp && close[i] < emaBuffer[i]))
            successCount++;
    }
    
    return (successCount >= InpSuccessConfirmBars);
}

//+------------------------------------------------------------------+
//| 计算EMA维持K线数                                                   |
//+------------------------------------------------------------------+
int CountEMAMaintainBars(const double &close[], const double &emaBuffer[], int breakPos, bool isUp)
{
    int count = 0;
    
    for(int i = breakPos - 1; i >= 0; i--)
    {
        if((isUp && close[i] > emaBuffer[i]) || (!isUp && close[i] < emaBuffer[i]))
            count++;
        else
            break;
    }
    
    return count;
}

//+------------------------------------------------------------------+
//| 检查重复客观事件                                                   |
//+------------------------------------------------------------------+
bool IsDuplicateObjectiveEvent(datetime eventTime, bool isUp)
{
    int timeWindow = 3600;  // 1小时内的事件视为重复
    
    for(int i = 0; i < ArraySize(historyEvents); i++)
    {
        if(historyEvents[i].isUpBreakout == isUp &&
           MathAbs((long)historyEvents[i].time - (long)eventTime) < timeWindow)
        {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 添加客观事件                                                       |
//+------------------------------------------------------------------+
void AddObjectiveEvent(const ObjectiveBreakoutEvent &event)
{
    int size = ArraySize(historyEvents);
    ArrayResize(historyEvents, size + 1);
    historyEvents[size] = event;
}

//+------------------------------------------------------------------+
//| 计算简单ATR                                                        |
//+------------------------------------------------------------------+
double CalculateSimpleATR(const double &high[], const double &low[], const double &close[], int pos, int period)
{
    double sum = 0.0;
    int count = 0;
    
    for(int i = pos; i < pos + period && i < ArraySize(high) - 1; i++)
    {
        double tr = MathMax(high[i] - low[i], 
                           MathMax(MathAbs(high[i] - close[i+1]), 
                                  MathAbs(low[i] - close[i+1])));
        sum += tr;
        count++;
    }
    
    return count > 0 ? sum / count : 0.0;
}

//+------------------------------------------------------------------+
//| 增量更新客观数据库                                                 |
//+------------------------------------------------------------------+
bool IncrementalUpdateObjective(int days)
{
    int updateBars = days * 24;  // 假设H1图表
    if(_Period == PERIOD_M15) updateBars = days * 96;
    else if(_Period == PERIOD_M5) updateBars = days * 288;
    else if(_Period == PERIOD_H4) updateBars = days * 6;
    else if(_Period == PERIOD_D1) updateBars = days;
    
    Print("增量更新最近 ", days, " 天的客观化特征数据 (", updateBars, " 根K线)");
    
    int originalSize = ArraySize(historyEvents);
    
    if(!AnalyzeObjectiveHistoricalData(0, updateBars))
        return false;
        
    int newEvents = ArraySize(historyEvents) - originalSize;
    Print("客观化增量更新完成，新增EMA突破事件: ", newEvents, " 个");
    
    // 更新数据库头信息
    dbHeader.lastUpdate = TimeCurrent();
    dbHeader.totalEvents = ArraySize(historyEvents);
    
    return SaveObjectiveDatabase();
}

//+------------------------------------------------------------------+
//| 保存客观数据库                                                     |
//+------------------------------------------------------------------+
bool SaveObjectiveDatabase()
{
    int handle = FileOpen(databaseFile, FILE_WRITE | FILE_BIN);
    if(handle == INVALID_HANDLE)
    {
        Print("无法创建客观化数据库文件: ", databaseFile);
        return false;
    }
    
    // 更新数据库头信息
    dbHeader.version = 5; // v4.0客观版本标识
    dbHeader.lastUpdate = TimeCurrent();
    dbHeader.totalEvents = ArraySize(historyEvents);
    StringToCharArray(_Symbol, dbHeader.symbol, 0, 11); // 将string转换为char数组
    dbHeader.timeframe = _Period;
    dbHeader.isObjectiveVersion = true;
    
    // 写入头信息
    FileWriteStruct(handle, dbHeader);
    
    // 写入特征权重
    FileWriteStruct(handle, featureWeights);
    
    // 写入事件数据
    for(int i = 0; i < ArraySize(historyEvents); i++)
    {
        FileWriteStruct(handle, historyEvents[i]);
    }
    
    FileClose(handle);
    Print("客观化数据库保存成功，总EMA突破事件数: ", ArraySize(historyEvents));
    return true;
}

//+------------------------------------------------------------------+
//| 加载客观数据库                                                     |
//+------------------------------------------------------------------+
bool LoadObjectiveDatabase()
{
    if(!FileIsExist(databaseFile))
    {
        Print("客观化数据库文件不存在: ", databaseFile);
        return false;
    }
    
    int handle = FileOpen(databaseFile, FILE_READ | FILE_BIN);
    if(handle == INVALID_HANDLE)
    {
        Print("无法打开客观化数据库文件: ", databaseFile);
        return false;
    }
    
    // 读取头信息
    FileReadStruct(handle, dbHeader);
    
    // 验证数据库版本和兼容性
    if(dbHeader.version != 5 || !dbHeader.isObjectiveVersion)
    {
        Print("数据库版本不兼容，需要重建 (当前版本: ", dbHeader.version, ")");
        FileClose(handle);
        return false;
    }
    
    string symbolStr = CharArrayToString(dbHeader.symbol);
    if(symbolStr != _Symbol || dbHeader.timeframe != _Period)
    {
        Print("数据库品种或周期不兼容: 品种=", symbolStr, " 周期=", dbHeader.timeframe);
        FileClose(handle);
        return false;
    }
    
    // 读取特征权重
    FileReadStruct(handle, featureWeights);
    
    // 读取事件数据
    ArrayResize(historyEvents, dbHeader.totalEvents);
    for(int i = 0; i < dbHeader.totalEvents; i++)
    {
        FileReadStruct(handle, historyEvents[i]);
    }
    
    FileClose(handle);
    Print("客观化数据库加载成功，EMA突破事件数: ", dbHeader.totalEvents);
    return true;
}

//+------------------------------------------------------------------+
//| 打印客观统计信息                                                   |
//+------------------------------------------------------------------+
void PrintObjectiveStatistics()
{
    int totalEvents = ArraySize(historyEvents);
    if(totalEvents == 0) return;
    
    int upBreakouts = 0, downBreakouts = 0;
    int upSuccess = 0, downSuccess = 0;
    double avgRSI = 0, avgEMADev = 0, avgAcceleration = 0, avgMomentum = 0;
    int sessionStats[3] = {0}; // 亚洲，欧洲，美洲
    
    for(int i = 0; i < totalEvents; i++)
    {
        ObjectiveBreakoutEvent event = historyEvents[i];
        
        if(event.isUpBreakout)
        {
            upBreakouts++;
            if(event.wasSuccessful) upSuccess++;
        }
        else
        {
            downBreakouts++;
            if(event.wasSuccessful) downSuccess++;
        }
        
        // 累计统计客观特征
        avgRSI += event.rsi14;
        avgEMADev += event.emaDeviation;
        avgAcceleration += MathAbs(event.priceAcceleration);
        avgMomentum += MathAbs(event.momentumShift);
        
        // 时段统计
        if(event.sessionType >= 0 && event.sessionType < 3)
            sessionStats[event.sessionType]++;
    }
    
    // 计算平均值
    avgRSI /= totalEvents;
    avgEMADev /= totalEvents;
    avgAcceleration /= totalEvents;
    avgMomentum /= totalEvents;
    
    Print("=== v4.0 客观化多维特征数据库统计 ===");
    Print("总EMA20突破事件: ", totalEvents);
    Print("向上突破EMA20: ", upBreakouts, " 次，成功: ", upSuccess, " 次，胜率: ", 
          upBreakouts > 0 ? (double)upSuccess/upBreakouts*100 : 0, "%");
    Print("向下突破EMA20: ", downBreakouts, " 次，成功: ", downSuccess, " 次，胜率: ", 
          downBreakouts > 0 ? (double)downSuccess/downBreakouts*100 : 0, "%");
    
    Print("=== 客观特征统计 ===");
    Print("平均RSI: ", NormalizeDouble(avgRSI, 1));
    Print("平均EMA偏离度(ATR倍数): ", NormalizeDouble(avgEMADev, 2));
    Print("平均价格加速度: ", NormalizeDouble(avgAcceleration, 3));
    Print("平均动量偏移: ", NormalizeDouble(avgMomentum, 2));
    
    Print("=== 时段分布 ===");
    Print("亚洲时段: ", sessionStats[0], " 次 (", NormalizeDouble((double)sessionStats[0]/totalEvents*100, 1), "%)");
    Print("欧洲时段: ", sessionStats[1], " 次 (", NormalizeDouble((double)sessionStats[1]/totalEvents*100, 1), "%)");
    Print("美洲时段: ", sessionStats[2], " 次 (", NormalizeDouble((double)sessionStats[2]/totalEvents*100, 1), "%)");
    
    Print("客观特征权重 - 时间:", NormalizeDouble(featureWeights.timeWeight, 2), 
          " 客观技术:", NormalizeDouble(featureWeights.objectiveWeight, 2),
          " 进阶价格行为:", NormalizeDouble(featureWeights.priceActionWeight, 2));
    Print("数据库最后更新: ", TimeToString(dbHeader.lastUpdate));
}

//+------------------------------------------------------------------+
//| 主函数 (Phase 2: 智能概率计算版本)                               |
//+------------------------------------------------------------------+
void OnTick()
{
    if(!IsNewBar() || !tradingEnabled)
        return;
        
    // 1. 检查是否在学习阶段
    if(isLearningPhase)
    {
        Comment("系统正在学习阶段，等待数据准备完成...");
        return;
    }
        
    // 2. 检查风险控制
    if(consecutiveLoss >= InpMaxConsecutiveLoss)
    {
        Comment(StringFormat("达到最大连续亏损限制 (%d)，暂停交易", InpMaxConsecutiveLoss));
        return;
    }
    
    if(PositionsTotal() > 0) // 已有持仓则不操作
    {
        Comment("当前有持仓，等待平仓...");
        return;
    }
    
    // 3. 检测EMA突破信号
    double currentPrice = iClose(_Symbol, PERIOD_CURRENT, 0);
    double currentEMA;
    double prevPrice = iClose(_Symbol, PERIOD_CURRENT, 1);
    double prevEMA;
    
    double ema_buffer[2];
    if(CopyBuffer(emaHandle, 0, 0, 2, ema_buffer) <= 0)
    {
        Comment("获取EMA数据失败");
        return;
    }
    
    currentEMA = ema_buffer[0];
    prevEMA = ema_buffer[1];
    
    double tolerance = InpEmaTolerancePips * _Point;

    bool upBreakout = (currentPrice > currentEMA + tolerance) && (prevPrice <= prevEMA);
    bool downBreakout = (currentPrice < currentEMA - tolerance) && (prevPrice >= prevEMA);

    if (!upBreakout && !downBreakout)
    {
        Comment(StringFormat("监控中... 当前价格: %.5f, EMA20: %.5f, 最后概率: %.1f%%", 
                            currentPrice, currentEMA, lastProbabilityScore*100));
        return;
    }

    // 4. 提取当前市场特征
    ObjectiveBreakoutEvent currentEvent;
    currentEvent.time = TimeCurrent();
    currentEvent.emaLevel = currentEMA;
    currentEvent.isUpBreakout = upBreakout;

    if (!ExtractCurrentMarketFeatures(currentEvent))
    {
        Print("错误：提取当前市场特征失败！");
        Comment("特征提取失败，跳过此次信号");
        return;
    }
    
    // 5. 计算高级概率
    if (!InpUseAdvancedProbability)
    {
        Print("高级概率计算已禁用，跳过交易决策");
        Comment("高级概率计算已禁用");
        return;
    }
    
    double probability = CalculateAdvancedProbability(currentEvent);
    lastProbabilityScore = probability;
    
    PrintFormat("=== EMA%d突破信号检测 ===", InpEmaPeriod);
    PrintFormat("突破方向: %s", upBreakout ? "向上" : "向下");
    PrintFormat("当前价格: %.5f, EMA20: %.5f", currentPrice, currentEMA);
    PrintFormat("计算概率: %.2f%% (阈值: %.2f%%)", probability * 100, InpProbThreshold * 100);
    PrintFormat("RSI: %.1f, EMA偏离: %.2f, 加速度: %.3f", 
                currentEvent.rsi14, currentEvent.emaDeviation, currentEvent.priceAcceleration);
    PrintFormat("动量偏移: %.2f, 多周期一致性: %.1f, 波动率突变: %.2f", 
                currentEvent.momentumShift, currentEvent.multiTFConsistency, currentEvent.volatilityBurst);

    // 6. 概率阈值过滤
    if (probability < InpProbThreshold)
    {
        PrintFormat("概率 %.2f%% 低于阈值 %.2f%%，不交易", probability * 100, InpProbThreshold * 100);
        Comment(StringFormat("信号概率不足: %.1f%% < %.1f%%", probability*100, InpProbThreshold*100));
        return;
    }
    
    PrintFormat("概率评估通过！准备执行智能交易，预期成功概率: %.2f%%", probability * 100);

    // 7. 执行智能交易
    bool tradeSuccess = false;
    if (upBreakout)
        tradeSuccess = ExecuteSmartTrade(ORDER_TYPE_BUY, probability, currentEvent);
    else if (downBreakout)
        tradeSuccess = ExecuteSmartTrade(ORDER_TYPE_SELL, probability, currentEvent);
        
    // 8. 更新交易统计
    if(tradeSuccess)
    {
        tradeStats.totalTrades++;
        tradeStats.probabilitySum += probability;
        
        // 按概率区间统计
        int probRange = (int)(probability * 10);
        if(probRange >= 0 && probRange < 10)
        {
            tradeStats.countByProbRange[probRange]++;
        }
        
        Comment(StringFormat("交易已执行 - 概率: %.1f%%, 总交易数: %d", 
                            probability*100, tradeStats.totalTrades));
        
        Print("智能交易执行成功，基于多维特征概率评估");
    }
    else
    {
        Print("智能交易执行失败");
        Comment("交易执行失败，请检查账户状态");
    }
    
    // 9. 定期更新数据库
    datetime currentTime = TimeCurrent();
    if(currentTime - lastDataUpdate > 86400) // 24小时
    {
        lastDataUpdate = currentTime;
        if((currentTime - dbHeader.lastUpdate) > 86400 * InpIncrementalDays)
        {
            Print("执行自动客观化数据库更新...");
            IncrementalUpdateObjective(InpIncrementalDays);
            
            // 重新计算标准化参数和时间统计
            if(CalculateFeatureNormalizationParams() && CalculateTimeFeatureStats())
            {
                Print("数据库更新完成，特征参数已重新计算");
            }
        }
    }
}

bool IsNewBar()
{
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(currentBarTime != lastBarTime)
    {
        lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

void OnDeinit(const int reason)
{
    Print("=== 概率突破系统 v4.0 Phase 2 停止 ===");
    
    // 输出最终统计报告
    if(tradeStats.totalTrades > 0)
    {
        double winRate = (double)tradeStats.successfulTrades / tradeStats.totalTrades * 100;
        double avgProbability = tradeStats.probabilitySum / tradeStats.totalTrades * 100;
        
        Print("=== 智能交易最终统计 ===");
        PrintFormat("总交易数: %d", tradeStats.totalTrades);
        PrintFormat("成功交易: %d (胜率: %.1f%%)", tradeStats.successfulTrades, winRate);
        PrintFormat("总收益: %.2f", tradeStats.totalReturn);
        PrintFormat("平均预测概率: %.1f%%", avgProbability);
        PrintFormat("连续亏损: %d (最大限制: %d)", consecutiveLoss, InpMaxConsecutiveLoss);
        
        // 概率准确性分析
        AnalyzeProbabilityAccuracy();
    }
    
    // 输出特征参数摘要
    if(normParams.sampleSize > 0)
    {
        Print("=== 特征标准化参数摘要 ===");
        PrintFormat("样本数量: %d", normParams.sampleSize);
        PrintFormat("RSI范围: %.1f - %.1f", normParams.rsiMin, normParams.rsiMax);
        PrintFormat("EMA偏离度范围: %.2f - %.2f", normParams.emaDeviationMin, normParams.emaDeviationMax);
    }
    
    // 输出时间特征统计
    if(timeStats.isCalculated)
    {
        Print("=== 时间特征最优时段 ===");
        
        // 找出最佳交易时段
        double bestSessionRate = 0.0;
        int bestSession = -1;
        for(int i = 0; i < 3; i++)
        {
            if(timeStats.sessionSuccessRate[i] > bestSessionRate)
            {
                bestSessionRate = timeStats.sessionSuccessRate[i];
                bestSession = i;
            }
        }
        
        if(bestSession >= 0)
        {
            string sessionName = (bestSession == 0) ? "亚洲" : (bestSession == 1) ? "欧洲" : "美洲";
            PrintFormat("最佳交易时段: %s (成功率: %.1f%%)", sessionName, bestSessionRate * 100);
        }
        
        // 找出最佳交易小时
        double bestHourRate = 0.0;
        int bestHour = -1;
        for(int i = 0; i < 24; i++)
        {
            if(timeStats.hourSampleCount[i] >= 5 && timeStats.hourSuccessRate[i] > bestHourRate)
            {
                bestHourRate = timeStats.hourSuccessRate[i];
                bestHour = i;
            }
        }
        
        if(bestHour >= 0)
        {
            PrintFormat("最佳交易小时: %d:00 (成功率: %.1f%%)", bestHour, bestHourRate * 100);
        }
    }
    
    // 保存数据库（确保包含最新数据）
    if(ArraySize(historyEvents) > 0)
    {
        SaveObjectiveDatabase();
        Print("客观化特征数据库已保存，包含 ", ArraySize(historyEvents), " 个EMA突破事件");
    }
    
    // 输出权重信息
    Print("=== 最终特征权重 ===");
    PrintFormat("时间特征: %.2f, 客观技术: %.2f, 进阶价格行为: %.2f", 
               featureWeights.timeWeight, featureWeights.objectiveWeight, featureWeights.priceActionWeight);
    
    Print("智能概率计算系统已安全停止，所有数据已保存");
}

//+------------------------------------------------------------------+
//| === Phase 2: 核心概率计算函数组 ===                              |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 特征标准化函数                                                    |
//+------------------------------------------------------------------+
double NormalizeFeature(double value, double minVal, double maxVal)
{
    if (maxVal - minVal == 0) return 0.5; // 避免除以零，返回中性值
    double normalized = (value - minVal) / (maxVal - minVal);
    return MathMax(0.0, MathMin(1.0, normalized)); // 钳位到0-1区间
}

//+------------------------------------------------------------------+
//| 计算特征标准化参数                                                |
//+------------------------------------------------------------------+
bool CalculateFeatureNormalizationParams()
{
    int totalEvents = ArraySize(historyEvents);
    if(totalEvents < InpMinSampleSize)
    {
        Print("样本数量不足 (", totalEvents, " < ", InpMinSampleSize, ")，无法计算标准化参数");
        return false;
    }
    
    // 初始化极值
    normParams.rsiMin = 100.0;
    normParams.rsiMax = 0.0;
    normParams.emaDeviationMin = 999.0;
    normParams.emaDeviationMax = 0.0;
    normParams.accelerationMin = 999.0;
    normParams.accelerationMax = -999.0;
    normParams.momentumMin = 999.0;
    normParams.momentumMax = -999.0;
    normParams.volumePriceRatioMin = 999.0;
    normParams.volumePriceRatioMax = 0.0;
    normParams.volatilityBurstMin = 999.0;
    normParams.volatilityBurstMax = 0.0;
    
    // 遍历历史事件，找到每个特征的极值
    for(int i = 0; i < totalEvents; i++)
    {
        ObjectiveBreakoutEvent event = historyEvents[i];
        
        // RSI
        normParams.rsiMin = MathMin(normParams.rsiMin, event.rsi14);
        normParams.rsiMax = MathMax(normParams.rsiMax, event.rsi14);
        
        // EMA偏离度
        normParams.emaDeviationMin = MathMin(normParams.emaDeviationMin, event.emaDeviation);
        normParams.emaDeviationMax = MathMax(normParams.emaDeviationMax, event.emaDeviation);
        
        // 价格加速度
        normParams.accelerationMin = MathMin(normParams.accelerationMin, event.priceAcceleration);
        normParams.accelerationMax = MathMax(normParams.accelerationMax, event.priceAcceleration);
        
        // 动量偏移
        normParams.momentumMin = MathMin(normParams.momentumMin, event.momentumShift);
        normParams.momentumMax = MathMax(normParams.momentumMax, event.momentumShift);
        
        // 成交量价格比
        normParams.volumePriceRatioMin = MathMin(normParams.volumePriceRatioMin, event.volumePriceRatio);
        normParams.volumePriceRatioMax = MathMax(normParams.volumePriceRatioMax, event.volumePriceRatio);
        
        // 波动率突变
        normParams.volatilityBurstMin = MathMin(normParams.volatilityBurstMin, event.volatilityBurst);
        normParams.volatilityBurstMax = MathMax(normParams.volatilityBurstMax, event.volatilityBurst);
    }
    
    normParams.lastCalculated = TimeCurrent();
    normParams.sampleSize = totalEvents;
    
    Print("=== 特征标准化参数计算完成 ===");
    Print("RSI范围: ", NormalizeDouble(normParams.rsiMin, 1), " - ", NormalizeDouble(normParams.rsiMax, 1));
    Print("EMA偏离度范围: ", NormalizeDouble(normParams.emaDeviationMin, 2), " - ", NormalizeDouble(normParams.emaDeviationMax, 2));
    Print("价格加速度范围: ", NormalizeDouble(normParams.accelerationMin, 3), " - ", NormalizeDouble(normParams.accelerationMax, 3));
    Print("动量偏移范围: ", NormalizeDouble(normParams.momentumMin, 2), " - ", NormalizeDouble(normParams.momentumMax, 2));
    Print("成交量价格比范围: ", NormalizeDouble(normParams.volumePriceRatioMin, 2), " - ", NormalizeDouble(normParams.volumePriceRatioMax, 2));
    Print("波动率突变范围: ", NormalizeDouble(normParams.volatilityBurstMin, 2), " - ", NormalizeDouble(normParams.volatilityBurstMax, 2));
    
    return true;
}

//+------------------------------------------------------------------+
//| 计算时间特征统计                                                  |
//+------------------------------------------------------------------+
bool CalculateTimeFeatureStats()
{
    int totalEvents = ArraySize(historyEvents);
    if(totalEvents < InpMinSampleSize) return false;
    
    // 初始化数组
    for(int i = 0; i < 24; i++)
    {
        timeStats.hourSuccessRate[i] = 0.0;
        timeStats.hourSampleCount[i] = 0;
    }
    for(int i = 0; i < 3; i++)
    {
        timeStats.sessionSuccessRate[i] = 0.0;
        timeStats.sessionSampleCount[i] = 0;
    }
    for(int i = 0; i < 7; i++)
    {
        timeStats.dayOfWeekSuccessRate[i] = 0.0;
        timeStats.dayOfWeekSampleCount[i] = 0;
    }
    
    // 统计成功率
    for(int i = 0; i < totalEvents; i++)
    {
        ObjectiveBreakoutEvent event = historyEvents[i];
        
        // 小时统计
        if(event.hourOfDay >= 0 && event.hourOfDay < 24)
        {
            timeStats.hourSampleCount[event.hourOfDay]++;
            if(event.wasSuccessful)
                timeStats.hourSuccessRate[event.hourOfDay]++;
        }
        
        // 时段统计
        if(event.sessionType >= 0 && event.sessionType < 3)
        {
            timeStats.sessionSampleCount[event.sessionType]++;
            if(event.wasSuccessful)
                timeStats.sessionSuccessRate[event.sessionType]++;
        }
        
        // 星期统计
        if(event.dayOfWeek >= 1 && event.dayOfWeek <= 7)
        {
            int dayIndex = event.dayOfWeek - 1; // 转换为0-6索引
            timeStats.dayOfWeekSampleCount[dayIndex]++;
            if(event.wasSuccessful)
                timeStats.dayOfWeekSuccessRate[dayIndex]++;
        }
    }
    
    // 计算成功率
    for(int i = 0; i < 24; i++)
    {
        if(timeStats.hourSampleCount[i] > 0)
            timeStats.hourSuccessRate[i] /= timeStats.hourSampleCount[i];
    }
    for(int i = 0; i < 3; i++)
    {
        if(timeStats.sessionSampleCount[i] > 0)
            timeStats.sessionSuccessRate[i] /= timeStats.sessionSampleCount[i];
    }
    for(int i = 0; i < 7; i++)
    {
        if(timeStats.dayOfWeekSampleCount[i] > 0)
            timeStats.dayOfWeekSuccessRate[i] /= timeStats.dayOfWeekSampleCount[i];
    }
    
    timeStats.isCalculated = true;
    Print("时间特征统计计算完成");
    return true;
}

//+------------------------------------------------------------------+
//| 核心高级概率计算函数                                              |
//+------------------------------------------------------------------+
double CalculateAdvancedProbability(const ObjectiveBreakoutEvent &event)
{
    if(!timeStats.isCalculated || normParams.sampleSize == 0)
    {
        Print("错误：时间统计或标准化参数未准备就绪");
        return 0.5; // 返回中性概率
    }
    
    double timeScore = 0.0;
    double objectiveScore = 0.0;
    double priceActionScore = 0.0;
    
    // === 1. 时间特征评分 ===
    if(InpEnableTimeFeatures)
    {
        double hourScore = 0.5, sessionScore = 0.5, dayScore = 0.5;
        
        // 小时评分
        if(event.hourOfDay >= 0 && event.hourOfDay < 24)
        {
            hourScore = timeStats.hourSuccessRate[event.hourOfDay];
        }
        
        // 时段评分
        if(event.sessionType >= 0 && event.sessionType < 3)
        {
            sessionScore = timeStats.sessionSuccessRate[event.sessionType];
        }
        
        // 星期评分
        if(event.dayOfWeek >= 1 && event.dayOfWeek <= 7)
        {
            int dayIndex = event.dayOfWeek - 1;
            dayScore = timeStats.dayOfWeekSuccessRate[dayIndex];
        }
        
        // 加权平均
        timeScore = hourScore * 0.4 + sessionScore * 0.3 + dayScore * 0.3;
    }
    
    // === 2. 客观技术特征评分 ===
    if(InpEnableObjectiveFeatures)
    {
        // RSI评分 (RSI在30-70之间更有利)
        double rsiNorm = NormalizeFeature(event.rsi14, normParams.rsiMin, normParams.rsiMax);
        double rsiScore;
        if(event.isUpBreakout)
            rsiScore = MathMax(0.0, (rsiNorm - 0.3) / 0.4); // 向上突破偏好RSI > 30
        else
            rsiScore = MathMax(0.0, (0.7 - rsiNorm) / 0.4); // 向下突破偏好RSI < 70
            
        rsiScore = MathMin(1.0, rsiScore);
        
        // EMA偏离度评分 (适度偏离更有利)
        double emaDevNorm = NormalizeFeature(event.emaDeviation, normParams.emaDeviationMin, normParams.emaDeviationMax);
        double emaDevScore = 1.0 - MathAbs(emaDevNorm - 0.5) * 2; // 中等偏离度最优
        
        objectiveScore = rsiScore * featureWeights.rsiWeight + 
                        emaDevScore * featureWeights.emaDeviationWeight;
        
        // 标准化
        double totalObjectiveWeight = featureWeights.rsiWeight + featureWeights.emaDeviationWeight;
        if(totalObjectiveWeight > 0)
            objectiveScore /= totalObjectiveWeight;
    }
    
    // === 3. 进阶价格行为特征评分 ===
    if(InpEnableAdvancedPriceAction)
    {
        // 价格加速度评分
        double accelNorm = NormalizeFeature(MathAbs(event.priceAcceleration), 
                                           MathAbs(normParams.accelerationMin), 
                                           MathAbs(normParams.accelerationMax));
        double accelScore = accelNorm; // 更高的加速度有利于突破
        
        // 动量偏移评分
        double momentumNorm = NormalizeFeature(MathAbs(event.momentumShift), 
                                              MathAbs(normParams.momentumMin), 
                                              MathAbs(normParams.momentumMax));
        double momentumScore = momentumNorm; // 更强的动量有利
        
        // 成交量价格比评分
        double volPriceNorm = NormalizeFeature(event.volumePriceRatio, 
                                              normParams.volumePriceRatioMin, 
                                              normParams.volumePriceRatioMax);
        double volPriceScore = volPriceNorm; // 更高的成交量价格比有利
        
        // 多周期一致性评分 (已经是0-1)
        double multiTFScore = event.multiTFConsistency;
        
        // 波动率突变评分
        double volBurstNorm = NormalizeFeature(event.volatilityBurst, 
                                              normParams.volatilityBurstMin, 
                                              normParams.volatilityBurstMax);
        double volBurstScore = 1.0 - MathAbs(volBurstNorm - 0.6) * 2.5; // 适度波动率突变最优
        volBurstScore = MathMax(0.0, MathMin(1.0, volBurstScore));
        
        priceActionScore = accelScore * featureWeights.accelerationWeight +
                          momentumScore * featureWeights.momentumWeight +
                          volPriceScore * featureWeights.volumePriceWeight +
                          multiTFScore * featureWeights.multiTFWeight +
                          volBurstScore * featureWeights.volatilityWeight;
        
        // 标准化
        double totalPriceActionWeight = featureWeights.accelerationWeight + 
                                       featureWeights.momentumWeight +
                                       featureWeights.volumePriceWeight +
                                       featureWeights.multiTFWeight +
                                       featureWeights.volatilityWeight;
        if(totalPriceActionWeight > 0)
            priceActionScore /= totalPriceActionWeight;
    }
    
    // === 4. 最终概率合成 ===
    double finalProbability = timeScore * featureWeights.timeWeight +
                              objectiveScore * featureWeights.objectiveWeight +
                              priceActionScore * featureWeights.priceActionWeight;
    
    // 确保在0-1范围内
    finalProbability = MathMax(0.0, MathMin(1.0, finalProbability));
    
    return finalProbability;
}

//+------------------------------------------------------------------+
//| 实时特征提取函数                                                  |
//+------------------------------------------------------------------+
bool ExtractCurrentMarketFeatures(ObjectiveBreakoutEvent &currentEvent)
{
    // 获取当前价格数据
    double high[], low[], close[];
    long volume[];  // 修改为long类型以匹配CopyTickVolume
    datetime time[];
    
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(volume, true);
    ArraySetAsSeries(time, true);
    
    if(CopyHigh(_Symbol, PERIOD_CURRENT, 0, 20, high) <= 0 ||
       CopyLow(_Symbol, PERIOD_CURRENT, 0, 20, low) <= 0 ||
       CopyClose(_Symbol, PERIOD_CURRENT, 0, 20, close) <= 0 ||
       CopyTickVolume(_Symbol, PERIOD_CURRENT, 0, 20, volume) <= 0 ||
       CopyTime(_Symbol, PERIOD_CURRENT, 0, 20, time) <= 0)
    {
        Print("获取当前市场数据失败");
        return false;
    }
    
    // 获取指标数据
    double rsi[], atr[], ema[], emaH4[];
    ArraySetAsSeries(rsi, true);
    ArraySetAsSeries(atr, true);
    ArraySetAsSeries(ema, true);
    ArraySetAsSeries(emaH4, true);
    
    if(CopyBuffer(rsiHandle, 0, 0, 5, rsi) <= 0 ||
       CopyBuffer(atrHandle, 0, 0, 5, atr) <= 0 ||
       CopyBuffer(emaHandle, 0, 0, 20, ema) <= 0 ||
       CopyBuffer(emaH4Handle, 0, 0, 5, emaH4) <= 0)
    {
        Print("获取指标数据失败");
        return false;
    }
    
    // 提取时间特征
    ExtractObjectiveTimeFeatures(currentEvent, time[0]);
    
    // 提取客观技术特征
    currentEvent.rsi14 = rsi[0];
    currentEvent.atr14_normalized = atr[0] / close[0] * 100;
    double deviation = MathAbs(close[0] - ema[0]);
    currentEvent.emaDeviation = deviation / atr[0];
    
    // 提取进阶价格行为特征
    ExtractAdvancedPriceActionFeatures(currentEvent, 0, high, low, close, volume, 
                                     ema, emaH4, currentEvent.isUpBreakout);
    
    return true;
}

//+------------------------------------------------------------------+
//| 计算基础手数                                                      |
//+------------------------------------------------------------------+
double CalculateBaseLotSize(double stopLossPips)
{
    if(stopLossPips <= 0) return SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);

    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = accountBalance * (InpRiskPercent / 100.0);
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

    double lossPerLot = stopLossPips * tickValue * (1.0 / tickSize);
    if(lossPerLot <= 0) return SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);

    double lots = riskAmount / lossPerLot;

    // 标准化手数
    lots = NormalizeDouble(lots, (int)MathLog10(1/SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP)));
    lots = MathMax(SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN), 
                   MathMin(SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX), lots));
    
    return lots;
}

//+------------------------------------------------------------------+
//| 智能交易执行函数                                                  |
//+------------------------------------------------------------------+
bool ExecuteSmartTrade(ENUM_ORDER_TYPE orderType, double probability, const ObjectiveBreakoutEvent &event)
{
    // 1. 动态止损止盈（基于ATR和概率）
    double atr_buffer[1];
    if(CopyBuffer(atrHandle, 0, 0, 1, atr_buffer) <= 0)
    {
        Print("获取ATR数据失败");
        return false;
    }
    double atrValue = atr_buffer[0];
    
    if (atrValue <= 0) atrValue = SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 20;

    // 基础止损距离：概率越高，止损越紧
    double slMultiplier = 2.5 - (probability * 1.0);
    slMultiplier = MathMax(1.5, MathMin(2.5, slMultiplier));
    double stopLossDistancePoints = atrValue * slMultiplier;

    // 基于特征的止损调整
    if (event.volatilityBurst > 1.5 && event.volatilityBurst < normParams.volatilityBurstMax * 0.8)
        stopLossDistancePoints *= 1.2;
    else if (event.volatilityBurst >= normParams.volatilityBurstMax * 0.8)
        stopLossDistancePoints *= 0.8;

    if (event.multiTFConsistency < 0.1)
        stopLossDistancePoints *= 0.8;

    double takeProfitDistancePoints = stopLossDistancePoints * InpRiskRewardRatio;

    // 2. 动态仓位计算（基于概率调整）
    double baseLotSize = CalculateBaseLotSize(stopLossDistancePoints / _Point);
    
    double lotMultiplier = 1.0 + ((probability - InpProbThreshold) / (1.0 - InpProbThreshold)) * 0.5;
    lotMultiplier = MathMax(1.0, MathMin(1.5, lotMultiplier));
    double adjustedLotSize = baseLotSize * lotMultiplier;
    adjustedLotSize = NormalizeDouble(adjustedLotSize, (int)MathLog10(1/SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP)));
    adjustedLotSize = MathMax(SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN), 
                             MathMin(SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX), adjustedLotSize));

    if(adjustedLotSize < SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN))
    {
        Print("计算手数过小: ", adjustedLotSize);
        return false;
    }

    // 3. 执行订单
    MqlTradeRequest request;
    MqlTradeResult result;
    ZeroMemory(request);
    ZeroMemory(result);
    
    double price = 0;
    double slPrice = 0;
    double tpPrice = 0;

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = adjustedLotSize;
    request.type_filling = ORDER_FILLING_FOK;
    request.deviation = 5;
    request.comment = StringFormat("SmartProb v4: P=%.2f", probability*100);

    if (orderType == ORDER_TYPE_BUY)
    {
        request.type = ORDER_TYPE_BUY;
        price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        request.price = price;
        slPrice = price - stopLossDistancePoints;
        tpPrice = price + takeProfitDistancePoints;
    }
    else if (orderType == ORDER_TYPE_SELL)
    {
        request.type = ORDER_TYPE_SELL;
        price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        request.price = price;
        slPrice = price + stopLossDistancePoints;
        tpPrice = price - takeProfitDistancePoints;
    }
    else return false;

    request.sl = NormalizeDouble(slPrice, _Digits);
    request.tp = NormalizeDouble(tpPrice, _Digits);
    
    PrintFormat("准备开单: %s, 手数: %.2f, 价格: %.5f, SL: %.5f (%.1f点), TP: %.5f (%.1f点), 概率: %.2f%%",
                (orderType == ORDER_TYPE_BUY ? "BUY" : "SELL"), adjustedLotSize, request.price,
                request.sl, stopLossDistancePoints/_Point, request.tp, takeProfitDistancePoints/_Point, probability*100);

    if (!OrderSend(request, result))
    {
        PrintFormat("订单发送失败: %d", result.retcode);
        return false;
    }
    PrintFormat("订单发送成功: Ticket #%d", result.order);
    return true;
}

//+------------------------------------------------------------------+
//| 交易事件处理 (Phase 2: 智能统计追踪)                              |
//+------------------------------------------------------------------+
void OnTrade()
{
    // 检查最近的交易结果
    if(HistorySelect(TimeCurrent() - 60, TimeCurrent())) // 检查最近1分钟的交易
    {
        int totalDeals = HistoryDealsTotal();
        for(int i = totalDeals - 1; i >= 0; i--)
        {
            ulong ticket = HistoryDealGetTicket(i);
            if(ticket > 0)
            {
                ENUM_DEAL_ENTRY entry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(ticket, DEAL_ENTRY);
                if(entry == DEAL_ENTRY_OUT) // 平仓交易
                {
                    double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                    string comment = HistoryDealGetString(ticket, DEAL_COMMENT);
                    
                    // 检查是否为智能概率交易
                    if(StringFind(comment, "SmartProb v4") >= 0)
                    {
                        bool isWin = profit > 0;
                        UpdateTradeResult(isWin, profit);
                        
                        PrintFormat("智能交易完成 - 盈亏: %.2f, 结果: %s", 
                                   profit, isWin ? "成功" : "失败");
                        break;
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 更新交易结果统计                                                  |
//+------------------------------------------------------------------+
void UpdateTradeResult(bool isWin, double profit)
{
    if(isWin)
    {
        tradeStats.successfulTrades++;
        consecutiveLoss = 0; // 重置连续亏损计数
    }
    else
    {
        consecutiveLoss++;
    }
    
    tradeStats.totalReturn += profit;
    tradeStats.lastUpdate = TimeCurrent();
    
    // 计算当前胜率
    double winRate = 0.0;
    if(tradeStats.totalTrades > 0)
        winRate = (double)tradeStats.successfulTrades / tradeStats.totalTrades * 100;
    
    PrintFormat("=== 交易统计更新 ===");
    PrintFormat("总交易数: %d, 成功: %d, 胜率: %.1f%%", 
                tradeStats.totalTrades, tradeStats.successfulTrades, winRate);
    PrintFormat("总收益: %.2f, 连续亏损: %d", tradeStats.totalReturn, consecutiveLoss);
    
    if(consecutiveLoss >= InpMaxConsecutiveLoss)
    {
        PrintFormat("警告：达到最大连续亏损限制 (%d)，已暂停交易", InpMaxConsecutiveLoss);
    }
}

//+------------------------------------------------------------------+
//| 分析概率准确性                                                    |
//+------------------------------------------------------------------+
void AnalyzeProbabilityAccuracy()
{
    if(tradeStats.totalTrades < 10) return; // 样本不足
    
    Print("=== 概率准确性分析 ===");
    for(int i = 0; i < 10; i++)
    {
        if(tradeStats.countByProbRange[i] > 0)
        {
            double accuracy = tradeStats.accuracyByProbRange[i] / tradeStats.countByProbRange[i] * 100;
            PrintFormat("概率区间 %d0-%d0%%: 交易数=%d, 实际胜率=%.1f%%", 
                       i, i+1, tradeStats.countByProbRange[i], accuracy);
        }
    }
}

//+------------------------------------------------------------------+
//| 动态权重优化 (简化版网格搜索)                                      |
//+------------------------------------------------------------------+
bool OptimizeFeatureWeights()
{
    int totalEvents = ArraySize(historyEvents);
    if(totalEvents < InpMinSampleSize * 2) // 需要足够样本
    {
        Print("样本不足，无法进行权重优化");
        return false;
    }
    
    Print("开始简化权重优化...");
    
    ObjectiveFeatureWeights bestWeights = featureWeights;
    double bestScore = EvaluateWeightPerformance(featureWeights);
    
    // 简单网格搜索 - 只调整主要权重
    double timeWeights[] = {0.20, 0.25, 0.30};
    double objWeights[] = {0.30, 0.35, 0.40};
    
    for(int i = 0; i < ArraySize(timeWeights); i++)
    {
        for(int j = 0; j < ArraySize(objWeights); j++)
        {
            ObjectiveFeatureWeights testWeights = featureWeights;
            testWeights.timeWeight = timeWeights[i];
            testWeights.objectiveWeight = objWeights[j];
            testWeights.priceActionWeight = 1.0 - timeWeights[i] - objWeights[j];
            
            if(testWeights.priceActionWeight > 0.25 && testWeights.priceActionWeight < 0.50)
            {
                double score = EvaluateWeightPerformance(testWeights);
                if(score > bestScore)
                {
                    bestScore = score;
                    bestWeights = testWeights;
                }
            }
        }
    }
    
    // 如果找到更好的权重组合
    if(bestScore > EvaluateWeightPerformance(featureWeights) * 1.05) // 至少5%提升
    {
        featureWeights = bestWeights;
        featureWeights.lastUpdate = TimeCurrent();
        
        PrintFormat("权重优化完成！新评分: %.3f (提升: %.1f%%)", 
                   bestScore, (bestScore / EvaluateWeightPerformance(featureWeights) - 1) * 100);
        PrintFormat("新权重 - 时间: %.2f, 客观: %.2f, 进阶: %.2f", 
                   featureWeights.timeWeight, featureWeights.objectiveWeight, featureWeights.priceActionWeight);
        return true;
    }
    
    Print("权重优化未发现显著改进");
    return false;
}

//+------------------------------------------------------------------+
//| 评估权重性能                                                      |
//+------------------------------------------------------------------+
double EvaluateWeightPerformance(const ObjectiveFeatureWeights &weights)
{
    if(!timeStats.isCalculated || normParams.sampleSize == 0) return 0.0;
    
    int correctPredictions = 0;
    int totalPredictions = 0;
    double totalReturn = 0.0;
    
    // 临时保存当前权重
    ObjectiveFeatureWeights originalWeights = featureWeights;
    featureWeights = weights;
    
    // 在历史数据上测试权重性能
    for(int i = 0; i < ArraySize(historyEvents); i++)
    {
        ObjectiveBreakoutEvent event = historyEvents[i];
        double probability = CalculateAdvancedProbability(event);
        
        if(probability >= InpProbThreshold)
        {
            totalPredictions++;
            if(event.wasSuccessful)
            {
                correctPredictions++;
                totalReturn += event.actualReturn;
            }
            else
            {
                totalReturn -= 0.01; // 假设固定亏损
            }
        }
    }
    
    // 恢复原权重
    featureWeights = originalWeights;
    
    if(totalPredictions == 0) return 0.0;
    
    double accuracy = (double)correctPredictions / totalPredictions;
    double avgReturn = totalReturn / totalPredictions;
    
    // 综合评分：准确性 + 平均收益
    return accuracy * 0.7 + (avgReturn > 0 ? avgReturn * 100 : 0) * 0.3;
} 