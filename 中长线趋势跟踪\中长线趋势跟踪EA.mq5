//+------------------------------------------------------------------+
//|                                    中长线趋势跟踪EA_内置指标版.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "基于MT5内置唐奇安通道指标的中长线趋势跟踪EA"

//--- 包含必要的头文件
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

//--- 输入参数
input group "=== 基本设置 ==="
input int      唐奇安周期 = 20;                    // 唐奇安通道周期
input double   手数 = 0.01;                        // 交易手数
input int      魔术号 = 123456;                   // EA魔术号

input group "=== 趋势过滤器 ==="
input bool     启用趋势过滤器 = true;               // 启用200周期EMA趋势过滤器
input int      EMA周期 = 200;                     // EMA均线周期（趋势过滤器）
input bool     启用收盘价确认 = true;               // 启用收盘价确认突破（避免假突破）

input group "=== 风险管理 ==="
input int      ATR周期 = 14;                     // ATR周期（计算市场波动性）
input double   ATR倍数 = 2.0;                    // ATR止损倍数（止损距离=ATR*此倍数）
input double   最大风险百分比 = 2.0;               // 每笔交易最大风险百分比（账户余额的%）
input bool     启用资金管理 = true;                // 启用=根据风险%计算手数，禁用=使用固定手数

input group "=== 退出策略优化 ==="
input bool     启用移动止损 = true;                // 启用ATR移动止损
input double   移动止损ATR倍数 = 3.0;              // 移动止损ATR倍数
input double   移动止损启动ATR倍数 = 2.0;          // 移动止损启动条件（盈利超过此ATR倍数后启动）
input bool     启用固定盈亏比止盈 = false;          // 启用固定盈亏比止盈
input double   盈亏比 = 3.0;                      // 盈亏比（止盈距离=止损距离*此倍数）

input group "=== 加仓策略 ==="
input bool     启用加仓 = false;                  // 启用金字塔加仓
input int      最大仓位数 = 3;                    // 最大仓位数量
input double   加仓间距ATR倍数 = 1.0;              // 加仓间距（ATR倍数）
input double   加仓手数递减比例 = 0.5;             // 加仓手数递减比例
input double   加仓总风险上限 = 4.0;               // 加仓总风险上限（账户余额的%）
input bool     加仓前移动止损到保本 = true;         // 加仓前将所有仓位止损移动到保本位置

input group "=== 交易时间 ==="
input bool     限制交易时间 = false;               // 是否限制交易时间
input int      开始小时 = 8;                      // 开始交易小时
input int      结束小时 = 22;                     // 结束交易小时

//--- 全局变量
CTrade         交易对象;
CPositionInfo  持仓信息;
CAccountInfo   账户信息;

int            唐奇安通道句柄;
int            ATR句柄;
int            EMA句柄;

double         唐奇安上轨[];
double         唐奇安下轨[];
double         ATR值[];
double         EMA值[];

datetime       最后交易时间 = 0;
double         最后开仓价格 = 0;                   // 记录最后开仓价格（用于加仓判断）
int            当前仓位数 = 0;                     // 当前仓位数量

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- 设置交易对象参数
    交易对象.SetExpertMagicNumber(魔术号);
    交易对象.SetDeviationInPoints(10);
    交易对象.SetTypeFilling(ORDER_FILLING_FOK);
    
    //--- 使用正确路径创建MT5唐奇安通道指标句柄
    Print("正在加载MT5唐奇安通道指标...");
    唐奇安通道句柄 = iCustom(_Symbol, PERIOD_CURRENT, "Free Indicators\\Donchian Channel", 唐奇安周期, true);
    
    //--- 创建ATR指标句柄
    ATR句柄 = iATR(_Symbol, PERIOD_CURRENT, ATR周期);
    
    //--- 创建EMA指标句柄（趋势过滤器）
    EMA句柄 = iMA(_Symbol, PERIOD_CURRENT, EMA周期, 0, MODE_EMA, PRICE_CLOSE);
    
    //--- 检查指标句柄是否创建成功
    if(唐奇安通道句柄 == INVALID_HANDLE)
    {
        Print("唐奇安通道指标创建失败！请检查指标是否存在于 Free Indicators 文件夹中");
        return INIT_FAILED;
    }
    
    if(ATR句柄 == INVALID_HANDLE)
    {
        Print("ATR指标创建失败！");
        return INIT_FAILED;
    }
    
    if(EMA句柄 == INVALID_HANDLE)
    {
        Print("EMA指标创建失败！");
        return INIT_FAILED;
    }
    
    Print("指标初始化成功！");
    Print("- 唐奇安通道指标句柄: ", 唐奇安通道句柄);
    Print("- ATR指标句柄: ", ATR句柄);
    Print("- EMA指标句柄: ", EMA句柄);
    Print("- 唐奇安周期: ", 唐奇安周期);
    Print("- EMA周期: ", EMA周期);
    Print("- 启用趋势过滤器: ", 启用趋势过滤器 ? "是" : "否");
    Print("- 启用收盘价确认: ", 启用收盘价确认 ? "是" : "否");
    Print("- 启用移动止损: ", 启用移动止损 ? "是" : "否");
    Print("- 移动止损ATR倍数: ", 移动止损ATR倍数);
    Print("- 移动止损启动ATR倍数: ", 移动止损启动ATR倍数);
    Print("- 启用固定盈亏比止盈: ", 启用固定盈亏比止盈 ? "是" : "否");
    Print("- 盈亏比: ", 盈亏比);
    Print("- 启用加仓: ", 启用加仓 ? "是" : "否");
    Print("- 最大仓位数: ", 最大仓位数);
    Print("- 加仓间距ATR倍数: ", 加仓间距ATR倍数);
    Print("- 加仓手数递减比例: ", 加仓手数递减比例);
    Print("- 加仓总风险上限: ", 加仓总风险上限, "%");
    Print("- 加仓前移动止损到保本: ", 加仓前移动止损到保本 ? "是" : "否");
    
    //--- 设置数组为时间序列
    ArraySetAsSeries(唐奇安上轨, true);
    ArraySetAsSeries(唐奇安下轨, true);
    ArraySetAsSeries(ATR值, true);
    ArraySetAsSeries(EMA值, true);
    
    Print("中长线趋势跟踪EA（优化版）初始化成功！");
    Print("新增功能：移动止损、固定盈亏比止盈、金字塔加仓");
    Print("使用MT5内置唐奇安通道指标，周期: ", 唐奇安周期);
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- 释放指标句柄
    if(唐奇安通道句柄 != INVALID_HANDLE) IndicatorRelease(唐奇安通道句柄);
    if(ATR句柄 != INVALID_HANDLE) IndicatorRelease(ATR句柄);
    if(EMA句柄 != INVALID_HANDLE) IndicatorRelease(EMA句柄);
    
    Print("中长线趋势跟踪EA（内置指标版）已卸载");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- 添加调试信息
    static int tick_count = 0;
    tick_count++;
    
    if(tick_count % 1000 == 0) // 每1000个tick输出一次状态
    {
        Print("EA运行中... Tick: ", tick_count, " 时间: ", TimeToString(TimeCurrent()));
    }
    
    //--- 检查是否为新K线
    if(!是否新K线()) return;
    
    Print("=== 新K线检测 ===");
    Print("当前时间: ", TimeToString(TimeCurrent()));
    
    //--- 检查交易时间
    if(限制交易时间 && !在交易时间内()) 
    {
        Print("不在交易时间内，跳过");
        return;
    }
    
    //--- 获取指标数据
    if(!获取指标数据()) 
    {
        Print("获取指标数据失败");
        return;
    }
    
    //--- 获取当前价格
    double 当前价格 = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double 卖价 = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    
    //--- 输出关键数据
    Print("当前价格: ", 当前价格);
    Print("唐奇安上轨[1]: ", 唐奇安上轨[1]);
    Print("唐奇安下轨[1]: ", 唐奇安下轨[1]);
    Print("ATR值[1]: ", ATR值[1]);
    if(启用趋势过滤器)
    {
        Print("EMA(", EMA周期, ")[1]: ", EMA值[1]);
    }
    
    //--- 检查持仓情况
    bool 有多头持仓 = 检查持仓类型(POSITION_TYPE_BUY);
    bool 有空头持仓 = 检查持仓类型(POSITION_TYPE_SELL);
    
    Print("有多头持仓: ", 有多头持仓 ? "是" : "否");
    Print("有空头持仓: ", 有空头持仓 ? "是" : "否");
    
    //--- 交易逻辑
    bool 多头信号 = false;
    bool 空头信号 = false;
    
    //--- 检查多头信号
    if(启用收盘价确认)
    {
        // 使用收盘价确认突破：上一根K线收盘价突破再上一根K线的通道
        double 上一根收盘价 = iClose(_Symbol, PERIOD_CURRENT, 1);
        多头信号 = (上一根收盘价 > 唐奇安上轨[2]);
        
        if(多头信号)
        {
            Print("*** 收盘价确认多头突破 ***");
            Print("上一根收盘价 ", 上一根收盘价, " > 唐奇安上轨[2] ", 唐奇安上轨[2]);
        }
    }
    else
    {
        // 原始逻辑：当前价格突破通道
        多头信号 = (当前价格 > 唐奇安上轨[1]);
        
        if(多头信号)
        {
            Print("*** 检测到多头信号 ***");
            Print("当前价格 ", 当前价格, " > 唐奇安上轨 ", 唐奇安上轨[1]);
        }
    }
    
    //--- 检查空头信号
    if(启用收盘价确认)
    {
        // 使用收盘价确认突破：上一根K线收盘价跌破再上一根K线的通道
        double 上一根收盘价 = iClose(_Symbol, PERIOD_CURRENT, 1);
        空头信号 = (上一根收盘价 < 唐奇安下轨[2]);
        
        if(空头信号)
        {
            Print("*** 收盘价确认空头突破 ***");
            Print("上一根收盘价 ", 上一根收盘价, " < 唐奇安下轨[2] ", 唐奇安下轨[2]);
        }
    }
    else
    {
        // 原始逻辑：当前价格跌破通道
        空头信号 = (当前价格 < 唐奇安下轨[1]);
        
        if(空头信号)
        {
            Print("*** 检测到空头信号 ***");
            Print("当前价格 ", 当前价格, " < 唐奇安下轨 ", 唐奇安下轨[1]);
        }
    }
    
    //--- 应用趋势过滤器
    if(启用趋势过滤器)
    {
        if(多头信号)
        {
            if(当前价格 > EMA值[1])
            {
                Print("趋势过滤器通过：当前价格 ", 当前价格, " > EMA(", EMA周期, ") ", EMA值[1]);
            }
            else
            {
                Print("趋势过滤器阻止多头信号：当前价格 ", 当前价格, " <= EMA(", EMA周期, ") ", EMA值[1]);
                多头信号 = false;
            }
        }
        
        if(空头信号)
        {
            if(当前价格 < EMA值[1])
            {
                Print("趋势过滤器通过：当前价格 ", 当前价格, " < EMA(", EMA周期, ") ", EMA值[1]);
            }
            else
            {
                Print("趋势过滤器阻止空头信号：当前价格 ", 当前价格, " >= EMA(", EMA周期, ") ", EMA值[1]);
                空头信号 = false;
            }
        }
    }
    
    //--- 更新当前仓位数
    当前仓位数 = 统计当前仓位数();
    
    //--- 处理移动止损
    if(启用移动止损)
    {
        处理移动止损();
    }
    
    //--- 执行交易信号
    if(多头信号)
    {
        if(有空头持仓) 
        {
            Print("先平空头仓位");
            平仓所有持仓();
            当前仓位数 = 0;
        }
        
        // 检查是否可以开仓或加仓
        if(!有多头持仓)
        {
            开多头仓位();
        }
        else if(启用加仓 && 当前仓位数 < 最大仓位数)
        {
            // 检查加仓条件
            if(检查加仓条件(POSITION_TYPE_BUY, 当前价格))
            {
                加仓多头();
            }
        }
    }
    else if(空头信号)
    {
        if(有多头持仓) 
        {
            Print("先平多头仓位");
            平仓所有持仓();
            当前仓位数 = 0;
        }
        
        // 检查是否可以开仓或加仓
        if(!有空头持仓)
        {
            开空头仓位();
        }
        else if(启用加仓 && 当前仓位数 < 最大仓位数)
        {
            // 检查加仓条件
            if(检查加仓条件(POSITION_TYPE_SELL, 当前价格))
            {
                加仓空头();
            }
        }
    }
    else
    {
        Print("无交易信号");
        if(当前价格 <= 唐奇安上轨[1] && 当前价格 >= 唐奇安下轨[1])
        {
            Print("价格在唐奇安通道内，等待突破");
        }
    }
}

//+------------------------------------------------------------------+
//| 检查是否为新K线                                                    |
//+------------------------------------------------------------------+
bool 是否新K线()
{
    static datetime 上次时间 = 0;
    datetime 当前时间 = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(当前时间 != 上次时间)
    {
        上次时间 = 当前时间;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 检查是否在交易时间内                                                |
//+------------------------------------------------------------------+
bool 在交易时间内()
{
    MqlDateTime 时间结构;
    TimeToStruct(TimeCurrent(), 时间结构);
    
    int 当前小时 = 时间结构.hour;
    return (当前小时 >= 开始小时 && 当前小时 <= 结束小时);
}

//+------------------------------------------------------------------+
//| 获取指标数据                                                      |
//+------------------------------------------------------------------+
bool 获取指标数据()
{
    //--- 如果有唐奇安通道句柄，使用内置指标
    if(唐奇安通道句柄 != INVALID_HANDLE)
    {
        // 缓冲区0: 上轨, 缓冲区1: 下轨
        if(CopyBuffer(唐奇安通道句柄, 0, 0, 3, 唐奇安上轨) < 3) 
        {
            Print("获取唐奇安上轨数据失败，使用备用方案");
            return 计算唐奇安通道();
        }
        
        if(CopyBuffer(唐奇安通道句柄, 1, 0, 3, 唐奇安下轨) < 3) 
        {
            Print("获取唐奇安下轨数据失败，使用备用方案");
            return 计算唐奇安通道();
        }
    }
    else
    {
        //--- 使用自定义计算方法
        if(!计算唐奇安通道()) return false;
    }
    
    //--- 获取ATR数据
    if(CopyBuffer(ATR句柄, 0, 0, 3, ATR值) < 3) 
    {
        Print("获取ATR数据失败");
        return false;
    }
    
    //--- 获取EMA数据（趋势过滤器）
    if(启用趋势过滤器)
    {
        if(CopyBuffer(EMA句柄, 0, 0, 3, EMA值) < 3) 
        {
            Print("获取EMA数据失败");
            return false;
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 自定义计算唐奇安通道                                               |
//+------------------------------------------------------------------+
bool 计算唐奇安通道()
{
    //--- 确保数组大小足够
    ArrayResize(唐奇安上轨, 3);
    ArrayResize(唐奇安下轨, 3);
    ArraySetAsSeries(唐奇安上轨, true);
    ArraySetAsSeries(唐奇安下轨, true);
    
    //--- 计算最近3根K线的唐奇安通道值
    for(int i = 0; i < 3; i++)
    {
        double 最高价 = 0;
        double 最低价 = DBL_MAX;
        
        //--- 查找过去N根K线的最高价和最低价
        for(int j = i + 1; j <= i + 唐奇安周期; j++)
        {
            double 高价 = iHigh(_Symbol, PERIOD_CURRENT, j);
            double 低价 = iLow(_Symbol, PERIOD_CURRENT, j);
            
            if(高价 > 最高价) 最高价 = 高价;
            if(低价 < 最低价) 最低价 = 低价;
        }
        
        唐奇安上轨[i] = 最高价;
        唐奇安下轨[i] = 最低价;
    }
    
    Print("使用自定义计算唐奇安通道成功");
    return true;
}

//+------------------------------------------------------------------+
//| 检查持仓类型                                                      |
//+------------------------------------------------------------------+
bool 检查持仓类型(ENUM_POSITION_TYPE 类型)
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(持仓信息.SelectByIndex(i))
        {
            if(持仓信息.Symbol() == _Symbol && 
               持仓信息.Magic() == 魔术号 && 
               持仓信息.PositionType() == 类型)
            {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 开多头仓位                                                        |
//+------------------------------------------------------------------+
void 开多头仓位()
{
    double 入场价 = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double 止损价 = 入场价 - ATR值[1] * ATR倍数;
    double 止盈价 = 启用固定盈亏比止盈 ? 入场价 + (入场价 - 止损价) * 盈亏比 : 0;
    
    //--- 计算手数
    double 交易手数 = 计算手数(入场价, 止损价);
    
    //--- 开仓
    if(交易对象.Buy(交易手数, _Symbol, 入场价, 止损价, 止盈价, "唐奇安多头"))
    {
        Print("开多头仓位成功 - 价格:", 入场价, " 止损:", 止损价, " 止盈:", 止盈价, " 手数:", 交易手数);
        最后交易时间 = TimeCurrent();
        最后开仓价格 = 入场价;
        当前仓位数++;
    }
    else
    {
        Print("开多头仓位失败 - 错误代码:", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 开空头仓位                                                        |
//+------------------------------------------------------------------+
void 开空头仓位()
{
    double 入场价 = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double 止损价 = 入场价 + ATR值[1] * ATR倍数;
    double 止盈价 = 启用固定盈亏比止盈 ? 入场价 - (止损价 - 入场价) * 盈亏比 : 0;
    
    //--- 计算手数
    double 交易手数 = 计算手数(入场价, 止损价);
    
    //--- 开仓
    if(交易对象.Sell(交易手数, _Symbol, 入场价, 止损价, 止盈价, "唐奇安空头"))
    {
        Print("开空头仓位成功 - 价格:", 入场价, " 止损:", 止损价, " 止盈:", 止盈价, " 手数:", 交易手数);
        最后交易时间 = TimeCurrent();
        最后开仓价格 = 入场价;
        当前仓位数++;
    }
    else
    {
        Print("开空头仓位失败 - 错误代码:", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 计算交易手数                                                      |
//| 资金管理详解：                                                    |
//| 1. 启用资金管理=true: 根据风险百分比计算手数                        |
//|    - 风险金额 = 账户余额 × 最大风险百分比                          |
//|    - 止损点数 = |入场价-止损价| ÷ 点值                           |
//|    - 交易手数 = 风险金额 ÷ (止损点数 × 每点价值)                   |
//| 2. 启用资金管理=false: 使用固定手数                               |
//| 3. 加仓风险控制: 确保总风险不超过上限                              |
//| 例：账户$10000，风险2%，止损50点 → 手数=$200÷(50×$1)=4手          |
//+------------------------------------------------------------------+
double 计算手数(double 入场价, double 止损价)
{
    // 如果未启用资金管理，直接返回固定手数
    if(!启用资金管理) 
    {
        Print("使用固定手数: ", 手数);
        return 手数;
    }
    
    //--- 获取账户信息
    double 账户余额 = 账户信息.Balance();
    double 风险金额 = 账户余额 * 最大风险百分比 / 100.0;
    
    //--- 如果是加仓，检查总风险控制
    if(启用加仓 && 当前仓位数 > 0)
    {
        double 当前总风险 = 计算当前总风险();
        double 总风险上限金额 = 账户余额 * 加仓总风险上限 / 100.0;
        
        Print("=== 加仓风险检查 ===");
        Print("当前总风险: $", 当前总风险);
        Print("总风险上限: $", 总风险上限金额);
        
        if(当前总风险 >= 总风险上限金额)
        {
            Print("加仓被阻止：总风险已达上限");
            return 0; // 不允许加仓
        }
        
        // 调整风险金额，确保不超过总风险上限
        double 剩余风险额度 = 总风险上限金额 - 当前总风险;
        风险金额 = MathMin(风险金额, 剩余风险额度);
        Print("调整后风险金额: $", 风险金额);
    }
    
    //--- 计算点值和点数
    double 点值 = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double 点大小 = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double 止损点数 = MathAbs(入场价 - 止损价) / 点大小;
    
    //--- 防止除零错误
    if(止损点数 <= 0 || 点值 <= 0)
    {
        Print("计算手数错误：止损点数或点值无效");
        return 手数; // 返回默认手数
    }
    
    //--- 计算理论手数
    double 理论手数 = 风险金额 / (止损点数 * 点值);
    
    //--- 获取交易品种限制
    double 最小手数 = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double 最大手数 = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double 手数步长 = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    //--- 标准化手数
    理论手数 = MathMax(理论手数, 最小手数);
    理论手数 = MathMin(理论手数, 最大手数);
    理论手数 = NormalizeDouble(理论手数 / 手数步长, 0) * 手数步长;
    
    //--- 输出计算详情
    Print("=== 资金管理计算 ===");
    Print("账户余额: $", 账户余额);
    Print("风险百分比: ", 最大风险百分比, "%");
    Print("风险金额: $", 风险金额);
    Print("止损点数: ", 止损点数);
    Print("每点价值: $", 点值);
    Print("计算手数: ", 理论手数);
    
    return 理论手数;
}

//+------------------------------------------------------------------+
//| 平仓所有持仓                                                      |
//+------------------------------------------------------------------+
void 平仓所有持仓()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(持仓信息.SelectByIndex(i))
        {
            if(持仓信息.Symbol() == _Symbol && 持仓信息.Magic() == 魔术号)
            {
                交易对象.PositionClose(持仓信息.Ticket());
                Print("平仓持仓 - 票号:", 持仓信息.Ticket());
            }
        }
    }
    当前仓位数 = 0;
    最后开仓价格 = 0;
}

//+------------------------------------------------------------------+
//| 统计当前仓位数                                                    |
//+------------------------------------------------------------------+
int 统计当前仓位数()
{
    int 仓位数 = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(持仓信息.SelectByIndex(i))
        {
            if(持仓信息.Symbol() == _Symbol && 持仓信息.Magic() == 魔术号)
            {
                仓位数++;
            }
        }
    }
    return 仓位数;
}

//+------------------------------------------------------------------+
//| 处理移动止损                                                      |
//+------------------------------------------------------------------+
void 处理移动止损()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(持仓信息.SelectByIndex(i))
        {
            if(持仓信息.Symbol() == _Symbol && 持仓信息.Magic() == 魔术号)
            {
                double 当前价格 = 持仓信息.PriceCurrent();
                double 开仓价格 = 持仓信息.PriceOpen();
                double 当前止损 = 持仓信息.StopLoss();
                double 新止损价 = 0;
                
                // 计算启动移动止损的最小盈利距离
                double 启动距离 = ATR值[1] * 移动止损启动ATR倍数;
                
                if(持仓信息.PositionType() == POSITION_TYPE_BUY)
                {
                    // 检查是否达到启动条件：盈利超过指定ATR倍数
                    if(当前价格 < 开仓价格 + 启动距离)
                    {
                        Print("多头移动止损未启动 - 盈利距离不足: ", 
                              NormalizeDouble((当前价格 - 开仓价格) / ATR值[1], 2), 
                              " ATR，需要: ", 移动止损启动ATR倍数, " ATR");
                        continue; // 跳过此仓位
                    }
                    
                    // 多头移动止损：止损价随价格上涨而上移
                    新止损价 = 当前价格 - ATR值[1] * 移动止损ATR倍数;
                    
                    // 只有当新止损价高于当前止损价时才更新
                    if(新止损价 > 当前止损 && 新止损价 > 开仓价格)
                    {
                        if(交易对象.PositionModify(持仓信息.Ticket(), 新止损价, 持仓信息.TakeProfit()))
                        {
                            Print("多头移动止损更新成功 - 新止损价:", 新止损价, " 原止损价:", 当前止损);
                        }
                    }
                }
                else if(持仓信息.PositionType() == POSITION_TYPE_SELL)
                {
                    // 检查是否达到启动条件：盈利超过指定ATR倍数
                    if(当前价格 > 开仓价格 - 启动距离)
                    {
                        Print("空头移动止损未启动 - 盈利距离不足: ", 
                              NormalizeDouble((开仓价格 - 当前价格) / ATR值[1], 2), 
                              " ATR，需要: ", 移动止损启动ATR倍数, " ATR");
                        continue; // 跳过此仓位
                    }
                    
                    // 空头移动止损：止损价随价格下跌而下移
                    新止损价 = 当前价格 + ATR值[1] * 移动止损ATR倍数;
                    
                    // 只有当新止损价低于当前止损价时才更新
                    if((新止损价 < 当前止损 || 当前止损 == 0) && 新止损价 < 开仓价格)
                    {
                        if(交易对象.PositionModify(持仓信息.Ticket(), 新止损价, 持仓信息.TakeProfit()))
                        {
                            Print("空头移动止损更新成功 - 新止损价:", 新止损价, " 原止损价:", 当前止损);
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 检查加仓条件                                                      |
//+------------------------------------------------------------------+
bool 检查加仓条件(ENUM_POSITION_TYPE 仓位类型, double 当前价格)
{
    if(最后开仓价格 == 0) return false;
    
    double 加仓间距 = ATR值[1] * 加仓间距ATR倍数;
    
    if(仓位类型 == POSITION_TYPE_BUY)
    {
        // 多头加仓：价格需要比上次开仓价格高出指定间距
        return (当前价格 >= 最后开仓价格 + 加仓间距);
    }
    else if(仓位类型 == POSITION_TYPE_SELL)
    {
        // 空头加仓：价格需要比上次开仓价格低出指定间距
        return (当前价格 <= 最后开仓价格 - 加仓间距);
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| 计算当前总风险                                                    |
//+------------------------------------------------------------------+
double 计算当前总风险()
{
    double 总风险 = 0;
    double 点值 = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double 点大小 = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(持仓信息.SelectByIndex(i))
        {
            if(持仓信息.Symbol() == _Symbol && 持仓信息.Magic() == 魔术号)
            {
                double 开仓价格 = 持仓信息.PriceOpen();
                double 止损价格 = 持仓信息.StopLoss();
                double 仓位手数 = 持仓信息.Volume();
                
                if(止损价格 > 0) // 如果设置了止损
                {
                    double 止损点数 = MathAbs(开仓价格 - 止损价格) / 点大小;
                    double 仓位风险 = 止损点数 * 点值 * 仓位手数;
                    总风险 += 仓位风险;
                }
            }
        }
    }
    
    return 总风险;
}

//+------------------------------------------------------------------+
//| 移动所有仓位止损到保本位置                                          |
//+------------------------------------------------------------------+
void 移动止损到保本()
{
    Print("=== 执行加仓前保本操作 ===");
    
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(持仓信息.SelectByIndex(i))
        {
            if(持仓信息.Symbol() == _Symbol && 持仓信息.Magic() == 魔术号)
            {
                double 开仓价格 = 持仓信息.PriceOpen();
                double 当前止损 = 持仓信息.StopLoss();
                double 保本止损价 = 开仓价格; // 保本位置
                
                // 根据仓位类型调整保本位置（考虑点差）
                double 点大小 = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
                
                if(持仓信息.PositionType() == POSITION_TYPE_BUY)
                {
                    // 多头：保本位置稍微低于开仓价，覆盖点差
                    保本止损价 = 开仓价格 - SymbolInfoInteger(_Symbol, SYMBOL_SPREAD) * 点大小;
                    
                    // 只有当保本止损价高于当前止损价时才更新
                    if(保本止损价 > 当前止损)
                    {
                        if(交易对象.PositionModify(持仓信息.Ticket(), 保本止损价, 持仓信息.TakeProfit()))
                        {
                            Print("多头仓位移动到保本成功 - 票号:", 持仓信息.Ticket(), " 新止损:", 保本止损价);
                        }
                    }
                }
                else if(持仓信息.PositionType() == POSITION_TYPE_SELL)
                {
                    // 空头：保本位置稍微高于开仓价，覆盖点差
                    保本止损价 = 开仓价格 + SymbolInfoInteger(_Symbol, SYMBOL_SPREAD) * 点大小;
                    
                    // 只有当保本止损价低于当前止损价时才更新
                    if(保本止损价 < 当前止损 || 当前止损 == 0)
                    {
                        if(交易对象.PositionModify(持仓信息.Ticket(), 保本止损价, 持仓信息.TakeProfit()))
                        {
                            Print("空头仓位移动到保本成功 - 票号:", 持仓信息.Ticket(), " 新止损:", 保本止损价);
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 加仓多头                                                          |
//+------------------------------------------------------------------+
void 加仓多头()
{
    //--- 加仓前移动止损到保本
    if(加仓前移动止损到保本)
    {
        移动止损到保本();
    }
    
    double 入场价 = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double 止损价 = 入场价 - ATR值[1] * ATR倍数;
    double 止盈价 = 启用固定盈亏比止盈 ? 入场价 + (入场价 - 止损价) * 盈亏比 : 0;
    
    //--- 计算递减手数
    double 基础手数 = 计算手数(入场价, 止损价);
    if(基础手数 <= 0)
    {
        Print("加仓被阻止：计算手数为0或风险超限");
        return;
    }
    
    double 加仓手数 = 基础手数 * MathPow(加仓手数递减比例, 当前仓位数);
    
    //--- 开仓
    if(交易对象.Buy(加仓手数, _Symbol, 入场价, 止损价, 止盈价, "唐奇安加仓多头"))
    {
        Print("加仓多头成功 - 价格:", 入场价, " 止损:", 止损价, " 手数:", 加仓手数, " 仓位数:", 当前仓位数 + 1);
        最后交易时间 = TimeCurrent();
        最后开仓价格 = 入场价;
        当前仓位数++;
    }
    else
    {
        Print("加仓多头失败 - 错误代码:", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 加仓空头                                                          |
//+------------------------------------------------------------------+
void 加仓空头()
{
    //--- 加仓前移动止损到保本
    if(加仓前移动止损到保本)
    {
        移动止损到保本();
    }
    
    double 入场价 = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double 止损价 = 入场价 + ATR值[1] * ATR倍数;
    double 止盈价 = 启用固定盈亏比止盈 ? 入场价 - (止损价 - 入场价) * 盈亏比 : 0;
    
    //--- 计算递减手数
    double 基础手数 = 计算手数(入场价, 止损价);
    if(基础手数 <= 0)
    {
        Print("加仓被阻止：计算手数为0或风险超限");
        return;
    }
    
    double 加仓手数 = 基础手数 * MathPow(加仓手数递减比例, 当前仓位数);
    
    //--- 开仓
    if(交易对象.Sell(加仓手数, _Symbol, 入场价, 止损价, 止盈价, "唐奇安加仓空头"))
    {
        Print("加仓空头成功 - 价格:", 入场价, " 止损:", 止损价, " 手数:", 加仓手数, " 仓位数:", 当前仓位数 + 1);
        最后交易时间 = TimeCurrent();
        最后开仓价格 = 入场价;
        当前仓位数++;
    }
    else
    {
        Print("加仓空头失败 - 错误代码:", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 交易事件处理                                                      |
//| 当有交易操作（开仓、平仓、修改）时自动触发                          |
//+------------------------------------------------------------------+
void OnTrade()
{
    //--- 获取最后一笔交易的详细信息
    if(HistorySelect(TimeCurrent() - 60, TimeCurrent())) // 查看最近1分钟的历史
    {
        int 历史订单总数 = HistoryDealsTotal();
        if(历史订单总数 > 0)
        {
            ulong 交易票号 = HistoryDealGetTicket(历史订单总数 - 1);
            if(交易票号 > 0)
            {
                //--- 获取交易信息
                long 交易魔术号 = HistoryDealGetInteger(交易票号, DEAL_MAGIC);
                if(交易魔术号 == 魔术号) // 只处理本EA的交易
                {
                    string 交易品种 = HistoryDealGetString(交易票号, DEAL_SYMBOL);
                    if(交易品种 == _Symbol) // 只处理当前品种
                    {
                        ENUM_DEAL_TYPE 交易类型 = (ENUM_DEAL_TYPE)HistoryDealGetInteger(交易票号, DEAL_TYPE);
                        double 交易手数 = HistoryDealGetDouble(交易票号, DEAL_VOLUME);
                        double 交易价格 = HistoryDealGetDouble(交易票号, DEAL_PRICE);
                        double 交易利润 = HistoryDealGetDouble(交易票号, DEAL_PROFIT);
                        datetime 交易时间 = (datetime)HistoryDealGetInteger(交易票号, DEAL_TIME);
                        
                        //--- 根据交易类型输出信息
                        switch(交易类型)
                        {
                            case DEAL_TYPE_BUY:
                                Print("=== 开多头仓位 ===");
                                Print("时间: ", TimeToString(交易时间));
                                Print("价格: ", 交易价格);
                                Print("手数: ", 交易手数);
                                break;
                                
                            case DEAL_TYPE_SELL:
                                Print("=== 开空头仓位 ===");
                                Print("时间: ", TimeToString(交易时间));
                                Print("价格: ", 交易价格);
                                Print("手数: ", 交易手数);
                                break;
                                
                            case DEAL_TYPE_BALANCE:
                                // 余额调整，通常是平仓
                                if(交易利润 != 0)
                                {
                                    Print("=== 平仓完成 ===");
                                    Print("时间: ", TimeToString(交易时间));
                                    Print("价格: ", 交易价格);
                                    Print("手数: ", 交易手数);
                                    Print("利润: $", 交易利润);
                                    
                                    //--- 计算盈亏统计
                                    static int 盈利次数 = 0;
                                    static int 亏损次数 = 0;
                                    static double 总利润 = 0;
                                    
                                    总利润 += 交易利润;
                                    if(交易利润 > 0) 
                                        盈利次数++;
                                    else if(交易利润 < 0) 
                                        亏损次数++;
                                    
                                    double 胜率 = (盈利次数 + 亏损次数 > 0) ? 
                                                (double)盈利次数 / (盈利次数 + 亏损次数) * 100 : 0;
                                    
                                    Print("--- 交易统计 ---");
                                    Print("盈利次数: ", 盈利次数);
                                    Print("亏损次数: ", 亏损次数);
                                    Print("胜率: ", NormalizeDouble(胜率, 2), "%");
                                    Print("累计利润: $", NormalizeDouble(总利润, 2));
                                }
                                break;
                        }
                    }
                }
            }
        }
    }
    
    //--- 检查当前持仓状态
    检查持仓风险();
}

//+------------------------------------------------------------------+
//| 检查持仓风险                                                      |
//+------------------------------------------------------------------+
void 检查持仓风险()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(持仓信息.SelectByIndex(i))
        {
            if(持仓信息.Symbol() == _Symbol && 持仓信息.Magic() == 魔术号)
            {
                double 当前利润 = 持仓信息.Profit();
                double 持仓手数 = 持仓信息.Volume();
                double 开仓价格 = 持仓信息.PriceOpen();
                double 当前价格 = 持仓信息.PriceCurrent();
                
                //--- 计算浮动盈亏百分比
                double 账户余额 = 账户信息.Balance();
                double 风险百分比 = (当前利润 / 账户余额) * 100;
                
                //--- 如果浮亏超过设定风险的150%，发出警告
                if(风险百分比 < -(最大风险百分比 * 1.5))
                {
                    Print("!!! 风险警告 !!!");
                    Print("当前浮亏: $", 当前利润);
                    Print("风险百分比: ", NormalizeDouble(风险百分比, 2), "%");
                    Print("超出预设风险: ", NormalizeDouble(最大风险百分比 * 1.5, 1), "%");
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 定时器事件处理                                                    |
//+------------------------------------------------------------------+
void OnTimer()
{
    //--- 每小时检查一次账户状态
    static datetime 上次检查时间 = 0;
    datetime 当前时间 = TimeCurrent();
    
    if(当前时间 - 上次检查时间 >= 3600) // 3600秒 = 1小时
    {
        上次检查时间 = 当前时间;
        
        //--- 输出账户状态
        Print("=== 账户状态检查 ===");
        Print("当前时间: ", TimeToString(当前时间));
        Print("账户余额: $", 账户信息.Balance());
        Print("账户净值: $", 账户信息.Equity());
        Print("可用保证金: $", 账户信息.FreeMargin());
        Print("已用保证金: $", 账户信息.Margin());
        
        //--- 检查保证金水平
        double 保证金水平 = 账户信息.MarginLevel();
        if(保证金水平 < 200 && 保证金水平 > 0) // 保证金水平低于200%
        {
            Print("!!! 保证金水平警告 !!!");
            Print("当前保证金水平: ", NormalizeDouble(保证金水平, 2), "%");
            Print("建议减少持仓或增加资金");
        }
        
        //--- 检查持仓数量
        int 持仓数量 = 0;
        for(int i = 0; i < PositionsTotal(); i++)
        {
            if(持仓信息.SelectByIndex(i))
            {
                if(持仓信息.Symbol() == _Symbol && 持仓信息.Magic() == 魔术号)
                {
                    持仓数量++;
                }
            }
        }
        Print("当前持仓数量: ", 持仓数量);
    }
}