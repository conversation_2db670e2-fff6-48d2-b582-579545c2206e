//+------------------------------------------------------------------+
//|                                            VolatilityAnalyzer.mqh |
//|                                  动态概率统计分析系统波动率分析器 |
//|                                                                  |
//+------------------------------------------------------------------+

#ifndef VOLATILITY_ANALYZER_MQH
#define VOLATILITY_ANALYZER_MQH

#include "Defines.mqh"
#include "DataCollector.mqh"

//+------------------------------------------------------------------+
//| 全局变量                                                         |
//+------------------------------------------------------------------+
static double g_average_daily_range_pips = 0.0;  // 历史平均日波幅

//+------------------------------------------------------------------+
//| 计算历史平均日波幅                                               |
//+------------------------------------------------------------------+
void Analyzer_CalculateAverageVolatility()
  {
   Print("开始计算历史平均日波幅...");
   
   g_average_daily_range_pips = 0.0;
   
   // 获取数据缓存大小
   int cache_size = Data_GetCacheSize();
   if(cache_size <= 0)
     {
      Print("没有可用的历史数据进行波动率分析");
      return;
     }
   
   double total_range_pips = 0.0;
   int valid_samples = 0;
   
   // 遍历所有历史数据
   for(int i = 0; i < cache_size; i++)
     {
      HistoricalDailyData data = Data_GetDailyDataAt(i);
      
      if(data.date_start_of_day == 0)
         continue;
      
      // 计算当日波幅（以点数为单位）
      double daily_range_pips = (data.high - data.low) / Point();
      
      // 过滤异常数据（波幅过小或过大）
      if(daily_range_pips > 0 && daily_range_pips < 10000)
        {
         total_range_pips += daily_range_pips;
         valid_samples++;
        }
     }
   
   // 计算平均值
   if(valid_samples > 0)
     {
      g_average_daily_range_pips = total_range_pips / valid_samples;
      Print("历史平均日波幅计算完成: ", StringFormat("%.1f", g_average_daily_range_pips), " 点 (基于 ", valid_samples, " 个有效样本)");
     }
   else
     {
      Print("没有有效的历史数据用于计算平均波幅");
     }
  }

//+------------------------------------------------------------------+
//| 获取历史平均日波幅                                               |
//+------------------------------------------------------------------+
double Analyzer_GetAverageDailyRangePips()
  {
   return g_average_daily_range_pips;
  }

//+------------------------------------------------------------------+
//| 获取当日已走波幅                                                 |
//+------------------------------------------------------------------+
double Analyzer_GetTodayRangePips(const string symbol)
  {
   // 获取当日最高价和最低价
   double today_high = iHigh(symbol, PERIOD_D1, 0);
   double today_low = iLow(symbol, PERIOD_D1, 0);
   
   if(today_high == 0 || today_low == 0)
     {
      return 0.0;
     }
   
   // 计算当日已走波幅（以点数为单位）
   double today_range_pips = (today_high - today_low) / Point();
   
   return today_range_pips;
  }

//+------------------------------------------------------------------+
//| 计算预估剩余空间                                                 |
//+------------------------------------------------------------------+
double Analyzer_GetEstimatedRemainingPips(const string symbol)
  {
   double avg_range = Analyzer_GetAverageDailyRangePips();
   double today_range = Analyzer_GetTodayRangePips(symbol);
   
   if(avg_range <= 0)
     {
      return 0.0;
     }
   
   double remaining = avg_range - today_range;
   
   // 确保不返回负值
   return MathMax(0.0, remaining);
  }

//+------------------------------------------------------------------+
//| 计算波幅完成百分比                                               |
//+------------------------------------------------------------------+
double Analyzer_GetCompletionPercent(const string symbol)
  {
   double avg_range = Analyzer_GetAverageDailyRangePips();
   double today_range = Analyzer_GetTodayRangePips(symbol);
   
   if(avg_range <= 0)
     {
      return 0.0;
     }
   
   double completion = (today_range / avg_range) * 100.0;
   
   // 限制在0-200%之间（允许超过平均值）
   return MathMax(0.0, MathMin(200.0, completion));
  }

//+------------------------------------------------------------------+
//| 获取波动率分析摘要                                               |
//+------------------------------------------------------------------+
string Analyzer_GetVolatilitySummary(const string symbol)
  {
   double avg_range = Analyzer_GetAverageDailyRangePips();
   double today_range = Analyzer_GetTodayRangePips(symbol);
   double remaining = Analyzer_GetEstimatedRemainingPips(symbol);
   double completion = Analyzer_GetCompletionPercent(symbol);
   
   string summary = StringFormat("平均波幅: %.1f点 | 今日已走: %.1f点 | 剩余空间: %.1f点 | 完成度: %.1f%%",
                                 avg_range, today_range, remaining, completion);
   
   return summary;
  }

//+------------------------------------------------------------------+
//| 判断当前波动率水平                                               |
//+------------------------------------------------------------------+
string Analyzer_GetVolatilityLevel(const string symbol)
  {
   double completion = Analyzer_GetCompletionPercent(symbol);
   
   if(completion < 30.0)
     {
      return "低波动";
     }
   else if(completion < 70.0)
     {
      return "正常波动";
     }
   else if(completion < 100.0)
     {
      return "高波动";
     }
   else if(completion < 150.0)
     {
      return "极高波动";
     }
   else
     {
      return "异常波动";
     }
  }

//+------------------------------------------------------------------+
//| 获取波动率建议                                                   |
//+------------------------------------------------------------------+
string Analyzer_GetVolatilityAdvice(const string symbol)
  {
   double completion = Analyzer_GetCompletionPercent(symbol);
   
   if(completion < 30.0)
     {
      return "波动较小，可能有较大行情空间";
     }
   else if(completion < 70.0)
     {
      return "波动正常，关注突破信号";
     }
   else if(completion < 100.0)
     {
      return "波动较大，注意风险控制";
     }
   else if(completion < 150.0)
     {
      return "波动极大，谨慎操作";
     }
   else
     {
      return "异常波动，建议观望";
     }
  }

#endif // VOLATILITY_ANALYZER_MQH
