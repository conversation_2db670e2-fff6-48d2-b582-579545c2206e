//+------------------------------------------------------------------+
//|                                                DataCollector.mqh |
//|                                动态概率统计分析系统历史数据加载器 |
//|                                                                  |
//+------------------------------------------------------------------+

#ifndef DATA_COLLECTOR_MQH
#define DATA_COLLECTOR_MQH

#include "Defines.mqh"

//+------------------------------------------------------------------+
//| 数据缓存区                                                       |
//+------------------------------------------------------------------+
static HistoricalDailyData g_daily_data_cache[];

//+------------------------------------------------------------------+
//| 加载历史数据                                                     |
//+------------------------------------------------------------------+
int Data_LoadHistory(const string symbol, const int days_to_load)
  {
   Print("开始加载历史数据: ", symbol, ", 请求天数: ", days_to_load);
   
   // 清空缓存数组
   ArrayResize(g_daily_data_cache, 0);
   
   // 请求日线数据
   MqlRates rates[];
   int copied = CopyRates(symbol, PERIOD_D1, 0, days_to_load, rates);
   
   if(copied <= 0)
     {
      Print("数据加载失败，错误代码: ", GetLastError());
      return 0;
     }
   
   Print("从服务器获取到 ", copied, " 条日线数据");
   
   // 调整缓存数组大小
   ArrayResize(g_daily_data_cache, copied);
   
   // 填充缓存数据
   for(int i = 0; i < copied; i++)
     {
      // 获取当日零点时间戳
      MqlDateTime dt_struct;
      TimeToStruct(rates[i].time, dt_struct);
      dt_struct.hour = 0;
      dt_struct.min = 0;
      dt_struct.sec = 0;
      datetime day_start = StructToTime(dt_struct);
      
      // 填充数据结构
      g_daily_data_cache[i].date_start_of_day = day_start;
      g_daily_data_cache[i].open = rates[i].open;
      g_daily_data_cache[i].high = rates[i].high;
      g_daily_data_cache[i].low = rates[i].low;
      g_daily_data_cache[i].close = rates[i].close;
      g_daily_data_cache[i].day_of_week = dt_struct.day_of_week;
      g_daily_data_cache[i].month_of_year = dt_struct.mon;
     }
   
   Print("历史数据加载完成，缓存 ", ArraySize(g_daily_data_cache), " 天数据");
   
   return ArraySize(g_daily_data_cache);
  }

//+------------------------------------------------------------------+
//| 获取缓存数据量                                                   |
//+------------------------------------------------------------------+
int Data_GetCacheSize()
  {
   return ArraySize(g_daily_data_cache);
  }

//+------------------------------------------------------------------+
//| 按索引获取日线数据                                               |
//+------------------------------------------------------------------+
HistoricalDailyData Data_GetDailyDataAt(int index)
  {
   HistoricalDailyData empty_data;
   empty_data.date_start_of_day = 0;
   empty_data.open = 0;
   empty_data.high = 0;
   empty_data.low = 0;
   empty_data.close = 0;
   empty_data.day_of_week = 0;
   empty_data.month_of_year = 0;
   
   if(index >= 0 && index < ArraySize(g_daily_data_cache))
     {
      return g_daily_data_cache[index];
     }
   
   return empty_data;
  }

//+------------------------------------------------------------------+
//| 获取缓存数据指针（供其他模块访问）                               |
//+------------------------------------------------------------------+
const HistoricalDailyData& Data_GetCacheData(const int index)
  {
   static HistoricalDailyData empty_data;
   
   if(index < 0 || index >= ArraySize(g_daily_data_cache))
     {
      Print("数据访问越界: index=", index, ", size=", ArraySize(g_daily_data_cache));
      return empty_data;
     }
   
   return g_daily_data_cache[index];
  }

//+------------------------------------------------------------------+
//| 检查数据是否需要更新                                             |
//+------------------------------------------------------------------+
bool Data_NeedsUpdate(const string symbol)
  {
   if(ArraySize(g_daily_data_cache) == 0)
      return true;
   
   // 获取最新的日线时间
   datetime latest_time = iTime(symbol, PERIOD_D1, 0);
   
   // 获取缓存中最新数据的时间
   datetime cache_latest = g_daily_data_cache[ArraySize(g_daily_data_cache) - 1].date_start_of_day;
   
   // 比较日期部分
   MqlDateTime latest_dt, cache_dt;
   TimeToStruct(latest_time, latest_dt);
   TimeToStruct(cache_latest, cache_dt);
   
   // 如果年月日不同，说明需要更新
   if(latest_dt.year != cache_dt.year || 
      latest_dt.mon != cache_dt.mon || 
      latest_dt.day != cache_dt.day)
     {
      return true;
     }
   
   return false;
  }

//+------------------------------------------------------------------+
//| 获取数据统计信息                                                 |
//+------------------------------------------------------------------+
string Data_GetStatistics()
  {
   int size = ArraySize(g_daily_data_cache);
   if(size == 0)
      return "无数据";
   
   datetime oldest = g_daily_data_cache[0].date_start_of_day;
   datetime newest = g_daily_data_cache[size - 1].date_start_of_day;
   
   string info = "数据范围: " + TimeToString(oldest, TIME_DATE) + 
                 " 至 " + TimeToString(newest, TIME_DATE) + 
                 " (共" + (string)size + "天)";
   
   return info;
  }

#endif // DATA_COLLECTOR_MQH