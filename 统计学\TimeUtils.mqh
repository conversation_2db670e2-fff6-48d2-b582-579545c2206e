//+------------------------------------------------------------------+
//|                                                    TimeUtils.mqh |
//|                                  动态概率统计分析系统时间工具模块 |
//|                                                                  |
//+------------------------------------------------------------------+

#ifndef TIME_UTILS_MQH
#define TIME_UTILS_MQH

#include "Defines.mqh"

//+------------------------------------------------------------------+
//| 获取北京时间                                                     |
//+------------------------------------------------------------------+
datetime Time_GetBeijingTime()
  {
   // 获取GMT时间并转换为北京时间 (UTC+8)
   datetime gmt_time = TimeGMT();
   datetime beijing_time = gmt_time + 8 * 3600;
   
   return beijing_time;
  }

//+------------------------------------------------------------------+
//| 格式化时间为字符串                                               |
//+------------------------------------------------------------------+
string Time_Format(const datetime dt)
  {
   // 格式化为 YYYY.MM.DD HH:MI:SS 格式
   return TimeToString(dt, TIME_DATE|TIME_SECONDS);
  }

//+------------------------------------------------------------------+
//| 获取交易时段                                                     |
//+------------------------------------------------------------------+
ENUM_TRADING_SESSION Time_GetSession(const datetime beijing_time, string &session_string)
  {
   // 将时间分解为结构体
   MqlDateTime dt_struct;
   TimeToStruct(beijing_time, dt_struct);
   
   int hour = dt_struct.hour;
   
   // 根据小时判断交易时段
   if(hour >= 7 && hour <= 14)
     {
      // 亚盘: 07:00 - 14:59
      session_string = "亚盘时段";
      return SESSION_ASIA;
     }
   else if(hour >= 15 && hour <= 19)
     {
      // 欧盘: 15:00 - 19:59
      session_string = "欧盘时段";
      return SESSION_EUROPE;
     }
   else if(hour >= 20 && hour <= 23)
     {
      // 欧美交叉: 20:00 - 23:59
      session_string = "欧美交叉时段";
      return SESSION_US_EUROPE_CROSS;
     }
   else if(hour >= 0 && hour <= 4)
     {
      // 美盘后半夜: 00:00 - 04:59
      session_string = "美盘后半夜";
      return SESSION_US_NIGHT;
     }
   else
     {
      // 其他时间: 05:00 - 06:59
      session_string = "休市时段";
      return SESSION_NONE;
     }
  }

//+------------------------------------------------------------------+
//| 获取时段中文描述                                                 |
//+------------------------------------------------------------------+
string Time_GetSessionDescription(const ENUM_TRADING_SESSION session)
  {
   switch(session)
     {
      case SESSION_ASIA:
         return "亚盘时段 (07:00-14:59)";
      case SESSION_EUROPE:
         return "欧盘时段 (15:00-19:59)";
      case SESSION_US_EUROPE_CROSS:
         return "欧美交叉时段 (20:00-23:59)";
      case SESSION_US_NIGHT:
         return "美盘后半夜 (00:00-04:59)";
      case SESSION_NONE:
      default:
         return "休市时段 (05:00-06:59)";
     }
  }

//+------------------------------------------------------------------+
//| 判断是否为交易时段                                               |
//+------------------------------------------------------------------+
bool Time_IsTradingSession(const ENUM_TRADING_SESSION session)
  {
   return (session != SESSION_NONE);
  }

//+------------------------------------------------------------------+
//| 获取时段颜色                                                     |
//+------------------------------------------------------------------+
color Time_GetSessionColor(const ENUM_TRADING_SESSION session)
  {
   switch(session)
     {
      case SESSION_ASIA:
         return clrYellow;        // 亚盘 - 黄色
      case SESSION_EUROPE:
         return clrLightBlue;     // 欧盘 - 浅蓝色
      case SESSION_US_EUROPE_CROSS:
         return clrLightGreen;    // 欧美交叉 - 浅绿色
      case SESSION_US_NIGHT:
         return clrOrange;        // 美盘后半夜 - 橙色
      case SESSION_NONE:
      default:
         return clrGray;          // 休市 - 灰色
     }
  }

#endif // TIME_UTILS_MQH