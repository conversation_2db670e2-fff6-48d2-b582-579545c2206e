//+------------------------------------------------------------------+
//|                                        ProbabilityBreakout_v1.mq5 |
//|                               基于概率统计的简单突破交易系统         |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "概率交易系统 v1.0"
#property link      ""
#property version   "1.00"

//--- 输入参数
input group "=== 基础参数 ==="
input int      InpLookbackBars = 20;           // 回看K线数量
input double   InpBreakoutThreshold = 0.0001;  // 突破阈值（点数）
input double   InpMinVolMultiplier = 1.2;      // 最小成交量倍数

input group "=== 风险管理 ==="
input double   InpRiskPercent = 1.0;           // 每笔交易风险百分比
input double   InpRiskRewardRatio = 2.0;       // 风险回报比
input int      InpMaxConsecutiveLoss = 3;      // 最大连续亏损次数

input group "=== 交易时间 ==="
input int      InpStartHour = 9;               // 开始交易小时
input int      InpEndHour = 22;                // 结束交易小时

//--- 全局变量
int            consecutiveLoss = 0;             // 连续亏损计数
datetime       lastTradeTime = 0;              // 上次交易时间
double         accountBalance;                  // 账户余额
bool           tradingEnabled = true;           // 交易开关

//+------------------------------------------------------------------+
//| 专家顾问初始化函数                                                  |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("概率突破系统 v1.0 初始化");
    
    // 检查账户类型
    if(AccountInfoInteger(ACCOUNT_TRADE_MODE) != ACCOUNT_TRADE_MODE_DEMO)
    {
        Alert("警告：当前不是模拟账户！");
    }
    
    accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    Print("初始账户余额: ", accountBalance);
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 专家顾问主函数                                                     |
//+------------------------------------------------------------------+
void OnTick()
{
    // 检查交易条件
    if(!IsNewBar() || !IsInTradingTime() || !tradingEnabled)
        return;
    
    // 检查连续亏损限制
    if(consecutiveLoss >= InpMaxConsecutiveLoss)
    {
        Print("达到最大连续亏损限制，暂停交易");
        return;
    }
    
    // 如果有持仓，检查出场条件
    if(PositionsTotal() > 0)
    {
        CheckExitConditions();
        return;
    }
    
    // 分析入场机会
    AnalyzeEntryOpportunity();
}

//+------------------------------------------------------------------+
//| 分析入场机会                                                       |
//+------------------------------------------------------------------+
void AnalyzeEntryOpportunity()
{
    // 获取价格数据
    double high[], low[], close[], volume[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(volume, true);
    
    if(CopyHigh(_Symbol, PERIOD_CURRENT, 0, InpLookbackBars + 1, high) <= 0 ||
       CopyLow(_Symbol, PERIOD_CURRENT, 0, InpLookbackBars + 1, low) <= 0 ||
       CopyClose(_Symbol, PERIOD_CURRENT, 0, InpLookbackBars + 1, close) <= 0 ||
       CopyTickVolume(_Symbol, PERIOD_CURRENT, 0, InpLookbackBars + 1, volume) <= 0)
    {
        Print("数据获取失败");
        return;
    }
    
    // 计算关键水平
    double resistance = GetResistanceLevel(high);
    double support = GetSupportLevel(low);
    double currentPrice = close[0];
    double previousPrice = close[1];
    
    // 计算成交量条件
    bool volumeConfirm = IsVolumeConfirm(volume);
    
    // 多头突破信号
    if(currentPrice > resistance + InpBreakoutThreshold * _Point &&
       previousPrice <= resistance &&
       volumeConfirm)
    {
        double probability = CalculateBreakoutProbability(true, resistance, high, low, close);
        if(probability > 0.6)  // 胜率要求超过60%
        {
            OpenBuyPosition(resistance);
        }
    }
    
    // 空头突破信号
    if(currentPrice < support - InpBreakoutThreshold * _Point &&
       previousPrice >= support &&
       volumeConfirm)
    {
        double probability = CalculateBreakoutProbability(false, support, high, low, close);
        if(probability > 0.6)  // 胜率要求超过60%
        {
            OpenSellPosition(support);
        }
    }
}

//+------------------------------------------------------------------+
//| 计算阻力位                                                         |
//+------------------------------------------------------------------+
double GetResistanceLevel(const double &high[])
{
    double maxHigh = 0;
    for(int i = 1; i <= InpLookbackBars; i++)
    {
        if(high[i] > maxHigh)
            maxHigh = high[i];
    }
    return maxHigh;
}

//+------------------------------------------------------------------+
//| 计算支撑位                                                         |
//+------------------------------------------------------------------+
double GetSupportLevel(const double &low[])
{
    double minLow = 999999;
    for(int i = 1; i <= InpLookbackBars; i++)
    {
        if(low[i] < minLow)
            minLow = low[i];
    }
    return minLow;
}

//+------------------------------------------------------------------+
//| 检查成交量确认                                                     |
//+------------------------------------------------------------------+
bool IsVolumeConfirm(const double &volume[])
{
    if(ArraySize(volume) < 5)
        return false;
        
    double currentVolume = volume[0];
    double avgVolume = 0;
    
    for(int i = 1; i < 5; i++)
    {
        avgVolume += volume[i];
    }
    avgVolume /= 4;
    
    return (currentVolume > avgVolume * InpMinVolMultiplier);
}

//+------------------------------------------------------------------+
//| 计算突破概率（简化版本）                                             |
//+------------------------------------------------------------------+
double CalculateBreakoutProbability(bool isBuy, double level, 
                                   const double &high[], const double &low[], const double &close[])
{
    int touchCount = 0;
    int successCount = 0;
    
    // 统计历史上类似突破的成功率
    for(int i = 2; i < InpLookbackBars; i++)
    {
        if(isBuy)
        {
            // 检查向上突破
            if(close[i] > level && close[i+1] <= level)
            {
                touchCount++;
                // 检查后续3根K线是否维持突破
                if(i >= 3 && close[i-1] > level && close[i-2] > level && close[i-3] > level)
                {
                    successCount++;
                }
            }
        }
        else
        {
            // 检查向下突破
            if(close[i] < level && close[i+1] >= level)
            {
                touchCount++;
                // 检查后续3根K线是否维持突破
                if(i >= 3 && close[i-1] < level && close[i-2] < level && close[i-3] < level)
                {
                    successCount++;
                }
            }
        }
    }
    
    if(touchCount == 0)
        return 0.5;  // 没有历史数据时返回中性概率
        
    double probability = (double)successCount / touchCount;
    Print("突破概率计算：成功次数=", successCount, ", 总次数=", touchCount, ", 概率=", probability);
    
    return probability;
}

//+------------------------------------------------------------------+
//| 开多单                                                             |
//+------------------------------------------------------------------+
void OpenBuyPosition(double resistance)
{
    double price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double stopLoss = resistance - InpBreakoutThreshold * _Point;
    double takeProfit = price + (price - stopLoss) * InpRiskRewardRatio;
    
    double lots = CalculatePositionSize(price - stopLoss);
    
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lots;
    request.type = ORDER_TYPE_BUY;
    request.price = price;
    request.sl = stopLoss;
    request.tp = takeProfit;
    request.comment = "概率突破买入";
    
    if(OrderSend(request, result))
    {
        Print("买入订单成功：价格=", price, ", 止损=", stopLoss, ", 止盈=", takeProfit);
        lastTradeTime = TimeCurrent();
    }
    else
    {
        Print("买入订单失败：", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| 开空单                                                             |
//+------------------------------------------------------------------+
void OpenSellPosition(double support)
{
    double price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double stopLoss = support + InpBreakoutThreshold * _Point;
    double takeProfit = price - (stopLoss - price) * InpRiskRewardRatio;
    
    double lots = CalculatePositionSize(stopLoss - price);
    
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lots;
    request.type = ORDER_TYPE_SELL;
    request.price = price;
    request.sl = stopLoss;
    request.tp = takeProfit;
    request.comment = "概率突破卖出";
    
    if(OrderSend(request, result))
    {
        Print("卖出订单成功：价格=", price, ", 止损=", stopLoss, ", 止盈=", takeProfit);
        lastTradeTime = TimeCurrent();
    }
    else
    {
        Print("卖出订单失败：", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| 计算仓位大小                                                       |
//+------------------------------------------------------------------+
double CalculatePositionSize(double stopLossDistance)
{
    double riskAmount = AccountInfoDouble(ACCOUNT_BALANCE) * InpRiskPercent / 100.0;
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    
    double lots = (riskAmount * tickSize) / (stopLossDistance * tickValue);
    
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lots = MathMax(minLot, MathMin(maxLot, NormalizeDouble(lots / lotStep, 0) * lotStep));
    
    return lots;
}

//+------------------------------------------------------------------+
//| 检查出场条件                                                       |
//+------------------------------------------------------------------+
void CheckExitConditions()
{
    // 这里可以添加额外的出场逻辑
    // 目前依赖止盈止损
}

//+------------------------------------------------------------------+
//| 交易事件处理                                                       |
//+------------------------------------------------------------------+
void OnTrade()
{
    // 检查是否有交易结果
    if(HistorySelect(TimeCurrent() - 86400, TimeCurrent()))
    {
        int totalDeals = HistoryDealsTotal();
        if(totalDeals > 0)
        {
            ulong ticket = HistoryDealGetTicket(totalDeals - 1);
            if(HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol)
            {
                double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                
                if(profit < 0)
                {
                    consecutiveLoss++;
                    Print("连续亏损次数：", consecutiveLoss);
                }
                else
                {
                    consecutiveLoss = 0;  // 重置连续亏损计数
                    Print("盈利交易，重置连续亏损计数");
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 检查是否新K线                                                      |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(currentBarTime != lastBarTime)
    {
        lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 检查是否在交易时间内                                                |
//+------------------------------------------------------------------+
bool IsInTradingTime()
{
    MqlDateTime time;
    TimeToStruct(TimeCurrent(), time);
    
    return (time.hour >= InpStartHour && time.hour < InpEndHour);
}

//+------------------------------------------------------------------+
//| 专家顾问反初始化函数                                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("概率突破系统停止，原因：", reason);
} 