//+------------------------------------------------------------------+
//|                                              周一开周三平EA.mq5 |
//|                                  Copyright 2023, Your Name      |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, Your Name"
#property link      "https://www.yourwebsite.com"
#property version   "1.00"
#property strict

// 引入交易库
#include <Trade\Trade.mqh>

// 输入参数
input long MagicNumber = 123456;           // EA魔术号 (用于识别和管理自己的订单) - 不同实例请用不同编号!
input double LotSize = 0.01;               // 交易手数
input ENUM_ORDER_TYPE TradeDirection = ORDER_TYPE_BUY; // 交易方向 (周一开仓方向: 买入 或 卖出)
input int MondayOpenHour = 9;              // 开仓时间 (周一, 小时, 0-23, 服务器时间)
input int MondayOpenMinute = 0;            // 开仓时间 (周一, 分钟, 0-59)
input int WednesdayCloseHour = 17;          // 平仓时间 (周三, 小时, 0-23, 服务器时间)
input int WednesdayCloseMinute = 0;         // 平仓时间 (周三, 分钟, 0-59)
input int StopLossPips = 50;                // 止损点数 (0 = 不设置止损)
input int TakeProfitPips = 100;             // 止盈点数 (0 = 不设置止盈)
input int Slippage = 3;                     // 允许的最大滑点 (points)
input string OrderComment = "MonOpenWedCloseEA"; // 订单注释

// 全局变量
CTrade trade;                               // 交易类实例
string eaName = "周一开仓周三平仓EA";       // EA 名称，用于日志输出

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 设置交易参数
   trade.SetExpertMagicNumber(MagicNumber);  // 设置魔术号
   trade.SetMarginMode();                    // 设置保证金模式
   trade.SetTypeFillingBySymbol(_Symbol);    // 设置订单执行类型
   trade.SetDeviationInPoints(Slippage);     // 设置允许的滑点
   
   // 设置定时器，每分钟检查一次
   EventSetTimer(60);
   
   // 打印初始化信息
   Print("====", eaName, "初始化完成====");
   Print("交易品种: ", _Symbol);
   Print("交易方向: ", TradeDirection == ORDER_TYPE_BUY ? "买入" : "卖出");
   Print("开仓时间: 周一 ", MondayOpenHour, ":", MondayOpenMinute < 10 ? "0" + IntegerToString(MondayOpenMinute) : IntegerToString(MondayOpenMinute));
   Print("平仓时间: 周三 ", WednesdayCloseHour, ":", WednesdayCloseMinute < 10 ? "0" + IntegerToString(WednesdayCloseMinute) : IntegerToString(WednesdayCloseMinute));
   Print("止损点数: ", StopLossPips, ", 止盈点数: ", TakeProfitPips);
   
   // 检查是否已有持仓
   if(DoesPositionExist(MagicNumber))
   {
      Print("检测到已有持仓，将监控平仓时间");
   }
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 停止定时器
   EventKillTimer();
   
   // 输出EA停止信息
   Print("====", eaName, "已停止，原因代码: ", reason, "====");
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
{
   // 获取当前服务器时间
   MqlDateTime currentTimeStruct;
   TimeCurrent(currentTimeStruct);
   
   // 提取星期几、小时和分钟
   int dayOfWeek = currentTimeStruct.day_of_week; // 0=周日, 1=周一, ..., 6=周六
   int hour = currentTimeStruct.hour;
   int minute = currentTimeStruct.min;
   
   // 调试信息
   // Print("当前时间: 星期", dayOfWeek, " ", hour, ":", minute < 10 ? "0" + IntegerToString(minute) : IntegerToString(minute));
   
   // 检查开仓条件 (周一指定时间)
   if(dayOfWeek == 1 && hour == MondayOpenHour && minute == MondayOpenMinute)
   {
      // 检查是否已有持仓
      bool positionExists = DoesPositionExist(MagicNumber);
      
      if(!positionExists)
      {
         // 尝试开仓
         Print("满足开仓条件，尝试开仓...");
         OpenTrade();
      }
      else
      {
         Print("已有持仓，不再开仓");
      }
   }
   // 检查平仓条件 (周三指定时间)
   else if(dayOfWeek == 3 && hour == WednesdayCloseHour && minute == WednesdayCloseMinute)
   {
      // 获取持仓票据
      ulong ticket = GetMagicPositionTicket(MagicNumber);
      
      if(ticket > 0)
      {
         // 尝试平仓
         Print("满足平仓条件，尝试平仓...");
         CloseTrade(ticket);
      }
      else
      {
         Print("没有需要平仓的持仓");
      }
   }
}

//+------------------------------------------------------------------+
//| 检查是否存在指定魔术号的持仓                                      |
//+------------------------------------------------------------------+
bool DoesPositionExist(long magic)
{
   // 遍历所有持仓
   for(int i = 0; i < PositionsTotal(); i++)
   {
      // 选择持仓
      ulong ticket = PositionGetTicket(i);
      if(ticket > 0)
      {
         // 检查魔术号和交易品种
         if(PositionGetInteger(POSITION_MAGIC) == magic && PositionGetString(POSITION_SYMBOL) == _Symbol)
         {
            return true; // 找到匹配的持仓
         }
      }
   }
   
   return false; // 未找到匹配的持仓
}

//+------------------------------------------------------------------+
//| 获取指定魔术号持仓的票据                                          |
//+------------------------------------------------------------------+
ulong GetMagicPositionTicket(long magic)
{
   // 遍历所有持仓
   for(int i = 0; i < PositionsTotal(); i++)
   {
      // 选择持仓
      ulong ticket = PositionGetTicket(i);
      if(ticket > 0)
      {
         // 检查魔术号和交易品种
         if(PositionGetInteger(POSITION_MAGIC) == magic && PositionGetString(POSITION_SYMBOL) == _Symbol)
         {
            return ticket; // 返回票据号
         }
      }
   }
   
   return 0; // 未找到匹配的持仓
}

//+------------------------------------------------------------------+
//| 开仓函数                                                         |
//+------------------------------------------------------------------+
void OpenTrade()
{
   // 获取当前价格
   double price = 0.0;
   double slPrice = 0.0; // 止损价格
   double tpPrice = 0.0; // 止盈价格
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT); // 一个点的价值
   
   // 根据交易方向设置价格和止损止盈
   if(TradeDirection == ORDER_TYPE_BUY)
   {
      // 买入使用卖价
      price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
      
      // 设置止损价格（如果启用）
      if(StopLossPips > 0)
         slPrice = price - StopLossPips * point;
      
      // 设置止盈价格（如果启用）
      if(TakeProfitPips > 0)
         tpPrice = price + TakeProfitPips * point;
   }
   else // ORDER_TYPE_SELL
   {
      // 卖出使用买价
      price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
      
      // 设置止损价格（如果启用）
      if(StopLossPips > 0)
         slPrice = price + StopLossPips * point;
      
      // 设置止盈价格（如果启用）
      if(TakeProfitPips > 0)
         tpPrice = price - TakeProfitPips * point;
   }
   
   // 执行交易
   bool result = false;
   if(TradeDirection == ORDER_TYPE_BUY)
   {
      result = trade.Buy(LotSize, _Symbol, price, slPrice, tpPrice, OrderComment);
   }
   else
   {
      result = trade.Sell(LotSize, _Symbol, price, slPrice, tpPrice, OrderComment);
   }
   
   // 检查交易结果
   if(result)
   {
      Print("开仓成功! 订单号: ", trade.ResultOrder(), ", 价格: ", trade.ResultPrice());
   }
   else
   {
      Print("开仓失败! 错误代码: ", trade.ResultRetcode(), ", 描述: ", trade.ResultComment());
   }
}

//+------------------------------------------------------------------+
//| 平仓函数                                                         |
//+------------------------------------------------------------------+
void CloseTrade(ulong ticket)
{
   // 尝试平仓
   bool result = trade.PositionClose(ticket, Slippage);
   
   // 检查平仓结果
   if(result)
   {
      Print("平仓成功! 订单号: ", ticket);
   }
   else
   {
      Print("平仓失败! 订单号: ", ticket, ", 错误代码: ", trade.ResultRetcode(), ", 描述: ", trade.ResultComment());
      // 注意: 此基础版本不包含平仓失败的重试逻辑
   }
}
//+------------------------------------------------------------------+