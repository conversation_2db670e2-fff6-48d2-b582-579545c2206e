//+------------------------------------------------------------------+
//|                                      SupportResistance.mqh     |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef SUPPORT_RESISTANCE_MQH
#define SUPPORT_RESISTANCE_MQH

#include "Parameters.mqh"              // 需要访问相关参数
#include "QualitySwingPointsTrendline.mqh" // 需要 SwingPointInfo 结构体和 DetectQualitySwingPoints 函数
#include <Arrays/ArrayDouble.mqh>      // 用于处理价格水平数组

//+------------------------------------------------------------------+
//| 支撑阻力水平信息结构体                                           |
//+------------------------------------------------------------------+
struct SRLevelInfo
{
   double price;     // S/R 水平的价格
   int    touches;   // 被测试（触及）的次数 (用于强度评估)
   int    last_touch_index; // 最近一次触及的 K 线索引 (用于评估时效性)
   // double score;  // 可以增加一个综合强度评分
};

//+------------------------------------------------------------------+
//| 基于高质量波动点识别支撑阻力水平                                 |
//+------------------------------------------------------------------+
void IdentifySRLevels(
     const MqlRates &rates[],         // 输入: 价格数据 (倒序)
     const double &atr_values[],      // 输入: ATR 数据 (倒序)
     const int lookback_period,     // 输入: 检测波动点的回看周期
     const int swing_def_bars,      // 输入: 波动点分形定义
     const double swing_ampl_mult,   // 输入: 波动点幅度 ATR 乘数
     const double min_swing_ampl,    // 输入: 波动点最小绝对幅度
     const int min_bars_swings,     // 输入: 波动点最小间隔
     const double merge_atr_factor,  // 输入: 合并水平位的 ATR 因子 (例如 0.3)
     double &support_levels[],      // 输出: 最终的支撑水平价格数组 (按价格升序排列)
     double &resistance_levels[]    // 输出: 最终的阻力水平价格数组 (按价格升序排列)
)
{
   // 初始化输出数组
   ArrayResize(support_levels, 0);
   ArrayResize(resistance_levels, 0);
   
   // 检查数据有效性
   int rates_total = ArraySize(rates);
   if(rates_total < lookback_period || lookback_period <= 2 * swing_def_bars)
   {
      Print("数据不足或参数设置不合理，无法识别支撑阻力位");
      return;
   }
   
   // a. 获取高质量波动点
   SwingPointInfo high_points[];
   SwingPointInfo low_points[];
   
   DetectQualitySwingPoints(
      rates, 
      atr_values, 
      lookback_period, 
      swing_def_bars, 
      swing_ampl_mult, 
      min_swing_ampl, 
      min_bars_swings, 
      high_points, 
      low_points
   );
   
   // 检查是否成功获取到波动点
   int high_count = ArraySize(high_points);
   int low_count = ArraySize(low_points);
   
   if(high_count == 0 && low_count == 0)
   {
      Print("未检测到有效的波动点，无法识别支撑阻力位");
      return;
   }
   
   // b. 提取初始水平位
   double initial_supports[];
   double initial_resistances[];
   
   // 从低点提取支撑位
   if(low_count > 0)
   {
      ArrayResize(initial_supports, low_count);
      for(int i = 0; i < low_count; i++)
      {
         initial_supports[i] = low_points[i].price;
      }
   }
   
   // 从高点提取阻力位
   if(high_count > 0)
   {
      ArrayResize(initial_resistances, high_count);
      for(int i = 0; i < high_count; i++)
      {
         initial_resistances[i] = high_points[i].price;
      }
   }
   
   // c. 合并相近水平位
   
   // 排序: 对初始支撑位和阻力位进行升序排序
   if(ArraySize(initial_supports) > 0)
      ArraySort(initial_supports);
   if(ArraySize(initial_resistances) > 0)
      ArraySort(initial_resistances);
   
   // 合并支撑位
   double merged_supports[];
   if(ArraySize(initial_supports) > 0)
   {
      // 获取当前ATR值用于合并判断
      double current_atr = atr_values[0]; // 使用最新的ATR值
      double merge_threshold = current_atr * merge_atr_factor;
      
      // 将第一个元素添加到合并数组
      ArrayResize(merged_supports, 1);
      merged_supports[0] = initial_supports[0];
      
      // 遍历剩余元素进行合并
      for(int i = 1; i < ArraySize(initial_supports); i++)
      {
         // 计算当前元素与合并数组最后一个元素的价格差
         double price_diff = initial_supports[i] - merged_supports[ArraySize(merged_supports) - 1];
         
         // 如果价格差小于等于合并阈值，则更新为平均值
         if(price_diff <= merge_threshold)
         {
            // 更新为两者的平均值
            merged_supports[ArraySize(merged_supports) - 1] = 
               (merged_supports[ArraySize(merged_supports) - 1] + initial_supports[i]) / 2.0;
         }
         else
         {
            // 如果价格差大于合并阈值，则添加为新的支撑位
            int new_size = ArraySize(merged_supports) + 1;
            ArrayResize(merged_supports, new_size);
            merged_supports[new_size - 1] = initial_supports[i];
         }
      }
   }
   
   // 合并阻力位
   double merged_resistances[];
   if(ArraySize(initial_resistances) > 0)
   {
      // 获取当前ATR值用于合并判断
      double current_atr = atr_values[0]; // 使用最新的ATR值
      double merge_threshold = current_atr * merge_atr_factor;
      
      // 将第一个元素添加到合并数组
      ArrayResize(merged_resistances, 1);
      merged_resistances[0] = initial_resistances[0];
      
      // 遍历剩余元素进行合并
      for(int i = 1; i < ArraySize(initial_resistances); i++)
      {
         // 计算当前元素与合并数组最后一个元素的价格差
         double price_diff = initial_resistances[i] - merged_resistances[ArraySize(merged_resistances) - 1];
         
         // 如果价格差小于等于合并阈值，则更新为平均值
         if(price_diff <= merge_threshold)
         {
            // 更新为两者的平均值
            merged_resistances[ArraySize(merged_resistances) - 1] = 
               (merged_resistances[ArraySize(merged_resistances) - 1] + initial_resistances[i]) / 2.0;
         }
         else
         {
            // 如果价格差大于合并阈值，则添加为新的阻力位
            int new_size = ArraySize(merged_resistances) + 1;
            ArrayResize(merged_resistances, new_size);
            merged_resistances[new_size - 1] = initial_resistances[i];
         }
      }
   }
   
   // e. 准备输出
   // 复制合并后的支撑位到输出数组
   int merged_supports_count = ArraySize(merged_supports);
   if(merged_supports_count > 0)
   {
      ArrayResize(support_levels, merged_supports_count);
      for(int i = 0; i < merged_supports_count; i++)
      {
         support_levels[i] = merged_supports[i];
      }
   }
   
   // 复制合并后的阻力位到输出数组
   int merged_resistances_count = ArraySize(merged_resistances);
   if(merged_resistances_count > 0)
   {
      ArrayResize(resistance_levels, merged_resistances_count);
      for(int i = 0; i < merged_resistances_count; i++)
      {
         resistance_levels[i] = merged_resistances[i];
      }
   }
}

//+------------------------------------------------------------------+
//| 绘制支撑阻力水平线                                               |
//+------------------------------------------------------------------+
void DrawSRLevels(
   const double &support_levels[],    // 支撑位数组
   const double &resistance_levels[], // 阻力位数组
   const color support_color = clrGreen,  // 支撑位线条颜色
   const color resistance_color = clrRed, // 阻力位线条颜色
   const int line_style = STYLE_DASH,     // 线条样式
   const int line_width = 1               // 线条宽度
)
{
   // 绘制支撑位
   for(int i = 0; i < ArraySize(support_levels); i++)
   {
      string line_name = "Support_" + DoubleToString(support_levels[i], _Digits);
      ObjectCreate(0, line_name, OBJ_HLINE, 0, 0, support_levels[i]);
      ObjectSetInteger(0, line_name, OBJPROP_COLOR, support_color);
      ObjectSetInteger(0, line_name, OBJPROP_STYLE, line_style);
      ObjectSetInteger(0, line_name, OBJPROP_WIDTH, line_width);
      ObjectSetInteger(0, line_name, OBJPROP_BACK, true); // 放在背景
      ObjectSetInteger(0, line_name, OBJPROP_SELECTABLE, false); // 不可选择
   }
   
   // 绘制阻力位
   for(int i = 0; i < ArraySize(resistance_levels); i++)
   {
      string line_name = "Resistance_" + DoubleToString(resistance_levels[i], _Digits);
      ObjectCreate(0, line_name, OBJ_HLINE, 0, 0, resistance_levels[i]);
      ObjectSetInteger(0, line_name, OBJPROP_COLOR, resistance_color);
      ObjectSetInteger(0, line_name, OBJPROP_STYLE, line_style);
      ObjectSetInteger(0, line_name, OBJPROP_WIDTH, line_width);
      ObjectSetInteger(0, line_name, OBJPROP_BACK, true); // 放在背景
      ObjectSetInteger(0, line_name, OBJPROP_SELECTABLE, false); // 不可选择
   }
}

//+------------------------------------------------------------------+
//| 清除所有支撑阻力水平线                                           |
//+------------------------------------------------------------------+
void ClearSRLevels()
{
   // 删除所有支撑位线
   for(int i = ObjectsTotal(0, 0, OBJ_HLINE) - 1; i >= 0; i--)
   {
      string obj_name = ObjectName(0, i, 0, OBJ_HLINE);
      if(StringFind(obj_name, "Support_") == 0 || StringFind(obj_name, "Resistance_") == 0)
      {
         ObjectDelete(0, obj_name);
      }
   }
}

#endif // SUPPORT_RESISTANCE_MQH