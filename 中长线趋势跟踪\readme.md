我们的目标是创建一个只在**H1（小时图）或H4（四小时图）**上运行的、规则极其简单的EA。它不追求完美的入场点，只求能识别并跟上一段明显的趋势。

选择模型（二选一）：

1、唐奇安通道 (Donchian Channel): 突破过去20根H1 K线的最高点就做多；跌破过去20根H1 K线的最低点就做空。这是最纯粹、最经典的趋势跟踪。
2、双EMA交叉 (Dual EMA Crossover): 当快速EMA（如EMA(21)）从下向上穿过慢速EMA（如EMA(55)）时做多（金叉）；反之做空（死叉）。

风险管理：

初始止损： 使用ATR（Average True Range）来设置。例如，止损位 = 入场价 - 2 * ATR(14)。这让止损能动态适应市场的波动性。

退出规则： 出现反向信号时平仓。例如，做多后，当价格跌破唐奇安通道的下轨或出现EMA死叉时，平掉所有多单。



第二步：优化进出场与资金管理 —— 精细化打击 (The Muscle)
▶ 为什么 (Why):

核心引擎解决了“方向”问题，但它的入场点和资金利用效率不高。我们可以在大方向正确的前提下，切换到更小的周期，寻找风险更小、潜在回报更高的“精确打击”机会，并通过金字塔加仓来放大战果。这就像航母战斗群（H1趋势）已经锁定了战区，现在派出战斗机（M5信号）去执行具体任务。

▶ 做什么 (What):

我们引入**多时钟框架（Multi-Timeframe, MTF）**分析。EA的核心逻辑依然基于H1，但所有的交易执行都在M5图表上。

状态过滤 (Regime Filter):

在OnTick()函数的最开始，首先检查H1图表的状态。例如，使用一条EMA(55)。

IF H1收盘价 > H1的EMA(55) THEN 市场处于“多头状态”，只允许在M5图表上寻找买入机会。

IF H1收盘价 < H1的EMA(55) THEN 市场处于“空头状态”，只允许在M5图表上寻找卖出机会。

否则，市场处于“观望状态”，M5图表上不进行任何操作。

M5精确入场与加仓：

初始入场： 在H1确认为“多头状态”后，等待M5图表上的价格从EMA(20)下方上穿，收盘在EMA(20)之上时，建立初始仓位。

金字塔加仓： 建立初始仓位后，如果价格继续上涨，然后回调至M5的EMA(20)附近获得支撑并再次收出阳线，则进行加仓。

资金管理： 初始仓位使用账户的1%风险。第一次加仓使用0.75%风险，第二次加仓使用0.5%... 确保风险敞口得到控制。

精细化出场：

移动止损： 这是必须的！对于所有持仓（初始仓+加仓），统一设置一个移动止损。例如，将总止损设置在M5图表上前一根K线的EMA(20)下方，或者当前价 - 1.5 * M5的ATR。



第三步：探索高级策略 —— 全天候适应性 (The Brain)
▶ 为什么 (Why):

趋势跟踪系统在震荡市中会持续亏损。为了让EA成为一个能创造稳定利润的“印钞机”，它必须能识别不同的市场环境，并采用不同的策略来应对。当市场没有趋势时，我们就不应该再使用趋势策略，而是切换到能从“波动”本身获利的策略。这就是你的想法二的用武之地。

▶ 做什么 (What):

我们在第二步的状态过滤器基础上，增加一个“盘整状态”的判断，并为其配备一个完全独立的交易模块。

盘整识别：

使用ADX指标。在H1图表上，IF ADX(14) < 20 THEN 市场处于“盘整状态”。这是业界公认的判断标准。

或者判断H1的EMA(21)和EMA(55)是否高度纠缠、距离很近。

构建独立的“盘整策略模块”：

这就是你的想法二：开盘波动率对冲网格策略。

触发器： 在EA中设置特定的交易时间，如亚盘开盘（服务器时间02:00）、欧盘开盘（09:00）、美盘开盘（15:30）。

核心逻辑：

在触发时间点，计算过去24小时的H1 ATR值，得到一个“预期日内波幅”。

在当前价的上下方，各开一个挂单（Buy Stop / Sell Stop），或者直接开多空对锁仓位。

止损/止盈距离就基于计算出的ATR值。

实现加仓和“V型反转紧急退出”逻辑。

策略调度中心：

OnTick()函数现在变成一个“调度员”。它首先判断H1的市场状态（趋势 or 盘整），然后调用不同的子函数去执行相应的策略。