//+------------------------------------------------------------------+
//|                                            PsyCoPilot_v3.mq5    |
//|                                    交易心理副驾 (终极版 V3.0)    |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "交易心理副驾 - Psy-CoPilot V3.0"
#property link      ""
#property version   "3.00"
#property indicator_chart_window
#property indicator_plots 0

//--- 输入参数
input ENUM_BASE_CORNER Corner = CORNER_LEFT_LOWER;           // 显示角落
input int FontSize = 14;                                      // 字体大小
input int Timer_Interval = 1;                                 // 定时器间隔(秒)
input int LosingStreak_Count = 3;                            // 连续亏损警告笔数
input double HeavyPosition_Margin_Percent = 50.0;            // 仓位过重警告百分比
input string Upcoming_News_Time = "2024.01.01 00:00";        // 下次重要新闻时间
input bool Enable_Blink_Effect = true;                       // 启用闪烁效果

//--- 颜色主题结构
struct ColorTheme
{
   color danger_bg;        // 危险状态背景色
   color danger_text;      // 危险状态文字色
   color warning_bg;       // 警告状态背景色
   color warning_text;     // 警告状态文字色
   color profit_bg;        // 盈利状态背景色
   color profit_text;      // 盈利状态文字色
   color normal_bg;        // 正常状态背景色
   color normal_text;      // 正常状态文字色
   color special_bg;       // 特殊状态背景色
   color special_text;     // 特殊状态文字色
};

//--- 全局变量
string label_name = "PsyCoPilot_Label";
datetime last_position_time = 0;
int consecutive_losses = 0;
double last_balance = 0;
bool position_just_opened = false;
datetime position_open_time = 0;
ColorTheme theme;
bool blink_state = false;
datetime last_blink_time = 0;

//--- 交易状态枚举
enum TRADE_STATE
{
   STATE_NO_POSITION,           // 空仓等待
   STATE_POSITION_JUST_OPENED,  // 刚刚开仓
   STATE_POSITION_IN_PROFIT,    // 持仓盈利
   STATE_POSITION_LOSING,       // 持仓亏损
   STATE_HEAVY_POSITION,        // 仓位过重
   STATE_LOSING_STREAK,         // 连续亏损
   STATE_HUGE_WIN,              // 巨大盈利
   STATE_LOW_VOLATILITY,        // 低波动
   STATE_APPROACHING_NEWS       // 临近新闻
};

//--- 模式枚举
enum TRADING_MODE
{
   MODE_DAY_SNIPER,    // 日内狙击手模式
   MODE_SWING_HUNTER   // 波段猎人模式
};

//+------------------------------------------------------------------+
//| 初始化颜色主题                                                    |
//+------------------------------------------------------------------+
void InitColorTheme()
{
   // 高对比度颜色方案 - 让交易者无法忽视
   theme.danger_bg = clrCrimson;           // 深红色背景 - 危险警告
   theme.danger_text = clrWhite;           // 白色文字 - 最高对比度
   theme.warning_bg = clrOrange;           // 橙色背景 - 警告状态
   theme.warning_text = clrBlack;          // 黑色文字 - 清晰可见
   theme.profit_bg = clrDarkGreen;         // 深绿色背景 - 盈利状态
   theme.profit_text = clrWhite;           // 白色文字 - 高对比度
   theme.normal_bg = clrNavy;              // 深蓝色背景 - 正常状态
   theme.normal_text = clrWhite;           // 白色文字 - 清晰可读
   theme.special_bg = clrPurple;           // 紫色背景 - 特殊状态
   theme.special_text = clrWhite;          // 白色文字 - 醒目显示
}

//+------------------------------------------------------------------+
//| 指标初始化函数                                                    |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化颜色主题
   InitColorTheme();
   
   // 创建标签对象
   if(!CreateLabel())
   {
      Print("创建标签失败");
      return INIT_FAILED;
   }
   
   // 启动定时器
   EventSetTimer(Timer_Interval);
   
   // 初始化账户余额
   last_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   
   Print("交易心理副驾 V3.0 已启动 - 准备直面交易真相！");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| 指标反初始化函数                                                  |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 删除标签
   ObjectDelete(0, label_name);
   
   // 停止定时器
   EventKillTimer();
   
   Print("交易心理副驾 V3.0 已停止");
}

//+------------------------------------------------------------------+
//| 指标计算函数                                                      |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   return rates_total;
}

//+------------------------------------------------------------------+
//| 定时器事件处理                                                    |
//+------------------------------------------------------------------+
void OnTimer()
{
   UpdatePsychologyLabel();
}

//+------------------------------------------------------------------+
//| 创建标签对象 - 优化显示版                                        |
//+------------------------------------------------------------------+
bool CreateLabel()
{
   if(!ObjectCreate(0, label_name, OBJ_LABEL, 0, 0, 0))
   {
      Print("创建标签对象失败: ", GetLastError());
      return false;
   }
   
   // 设置标签属性 - 优化显示效果
   ObjectSetInteger(0, label_name, OBJPROP_CORNER, Corner);
   ObjectSetInteger(0, label_name, OBJPROP_XDISTANCE, 10);
   ObjectSetInteger(0, label_name, OBJPROP_YDISTANCE, 25);
   ObjectSetInteger(0, label_name, OBJPROP_FONTSIZE, FontSize - 2);  // 稍微减小字体
   ObjectSetString(0, label_name, OBJPROP_FONT, "Microsoft YaHei");  // 使用微软雅黑，中文显示更好
   ObjectSetInteger(0, label_name, OBJPROP_COLOR, theme.normal_text);
   ObjectSetInteger(0, label_name, OBJPROP_BGCOLOR, theme.normal_bg);
   ObjectSetInteger(0, label_name, OBJPROP_BORDER_TYPE, BORDER_RAISED);
   ObjectSetInteger(0, label_name, OBJPROP_BORDER_COLOR, clrWhite);
   ObjectSetInteger(0, label_name, OBJPROP_BACK, false);
   ObjectSetInteger(0, label_name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, label_name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, label_name, OBJPROP_HIDDEN, true);
   
   return true;
}

//+------------------------------------------------------------------+
//| 获取当前交易模式                                                  |
//+------------------------------------------------------------------+
TRADING_MODE GetTradingMode()
{
   ENUM_TIMEFRAMES current_period = Period();
   
   // 小于等于1小时为日内狙击手模式
   if(current_period <= PERIOD_H1)
      return MODE_DAY_SNIPER;
   else
      return MODE_SWING_HUNTER;
}

//+------------------------------------------------------------------+
//| 获取当前交易状态                                                  |
//+------------------------------------------------------------------+
TRADE_STATE GetTradeState()
{
   // 检查是否有持仓
   if(PositionsTotal() == 0)
   {
      // 检查是否刚刚平仓
      CheckRecentTrades();
      
      // 检查连续亏损
      if(consecutive_losses >= LosingStreak_Count)
         return STATE_LOSING_STREAK;
         
      // 检查低波动
      if(IsLowVolatility())
         return STATE_LOW_VOLATILITY;
         
      // 检查临近新闻
      if(IsApproachingNews())
         return STATE_APPROACHING_NEWS;
         
      return STATE_NO_POSITION;
   }
   
   // 有持仓的情况
   double total_profit = GetTotalProfit();
   
   // 检查仓位是否过重
   if(IsHeavyPosition())
      return STATE_HEAVY_POSITION;
   
   // 检查是否刚刚开仓
   if(position_just_opened && TimeCurrent() - position_open_time < 300) // 5分钟内
      return STATE_POSITION_JUST_OPENED;
   
   // 检查盈亏状态
   if(total_profit > 0)
   {
      if(total_profit > AccountInfoDouble(ACCOUNT_BALANCE) * 0.05) // 盈利超过5%
         return STATE_HUGE_WIN;
      return STATE_POSITION_IN_PROFIT;
   }
   else if(total_profit < 0)
   {
      return STATE_POSITION_LOSING;
   }
   
   return STATE_NO_POSITION;
}

//+------------------------------------------------------------------+
//| 获取总盈亏                                                        |
//+------------------------------------------------------------------+
double GetTotalProfit()
{
   double total_profit = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket > 0 && PositionSelectByTicket(ticket))
      {
         total_profit += PositionGetDouble(POSITION_PROFIT);
      }
   }
   return total_profit;
}

//+------------------------------------------------------------------+
//| 检查是否仓位过重                                                  |
//+------------------------------------------------------------------+
bool IsHeavyPosition()
{
   double margin_used = AccountInfoDouble(ACCOUNT_MARGIN);
   double equity = AccountInfoDouble(ACCOUNT_EQUITY);
   
   if(equity <= 0) return false;
   
   double margin_percent = (margin_used / equity) * 100;
   return margin_percent > HeavyPosition_Margin_Percent;
}

//+------------------------------------------------------------------+
//| 检查是否低波动（简化版 - 基于价格波动）                           |
//+------------------------------------------------------------------+
bool IsLowVolatility()
{
   // 简化判断：基于最近几根K线的价格波动
   double high_prices[], low_prices[];
   int bars_to_check = 10;
   
   if(CopyHigh(_Symbol, _Period, 0, bars_to_check, high_prices) < bars_to_check ||
      CopyLow(_Symbol, _Period, 0, bars_to_check, low_prices) < bars_to_check)
      return false;
   
   double total_range = 0;
   for(int i = 0; i < bars_to_check; i++)
   {
      total_range += (high_prices[i] - low_prices[i]);
   }
   
   double avg_range = total_range / bars_to_check;
   double current_range = high_prices[0] - low_prices[0];
   
   // 当前波动小于平均波动的60%认为是低波动
   return current_range < avg_range * 0.6;
}

//+------------------------------------------------------------------+
//| 处理闪烁效果                                                      |
//+------------------------------------------------------------------+
void HandleBlinkEffect(TRADE_STATE state)
{
   if(!Enable_Blink_Effect) return;
   
   // 只有危险状态才闪烁
   if(state != STATE_HEAVY_POSITION && state != STATE_LOSING_STREAK) 
   {
      blink_state = false;
      return;
   }
   
   // 每500毫秒切换一次闪烁状态
   if(TimeCurrent() - last_blink_time >= 1)
   {
      blink_state = !blink_state;
      last_blink_time = TimeCurrent();
   }
}

//+------------------------------------------------------------------+
//| 检查是否临近新闻                                                  |
//+------------------------------------------------------------------+
bool IsApproachingNews()
{
   datetime news_time = StringToTime(Upcoming_News_Time);
   datetime current_time = TimeCurrent();
   
   // 新闻前15分钟内
   return (news_time - current_time) <= 900 && (news_time - current_time) > 0;
}

//+------------------------------------------------------------------+
//| 检查最近交易记录                                                  |
//+------------------------------------------------------------------+
void CheckRecentTrades()
{
   // 检查历史交易，统计连续亏损
   HistorySelect(TimeCurrent() - 86400, TimeCurrent()); // 最近24小时
   
   int total_deals = HistoryDealsTotal();
   int recent_losses = 0;
   
   for(int i = total_deals - 1; i >= 0; i--)
   {
      ulong ticket = HistoryDealGetTicket(i);
      if(ticket > 0)
      {
         if(HistoryDealGetInteger(ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
         {
            double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
            if(profit < 0)
               recent_losses++;
            else
               break; // 遇到盈利交易就停止计数
         }
      }
   }
   
   consecutive_losses = recent_losses;
}

//+------------------------------------------------------------------+
//| 检测新开仓                                                        |
//+------------------------------------------------------------------+
void CheckNewPosition()
{
   static int last_positions_count = 0;
   int current_positions_count = PositionsTotal();
   
   if(current_positions_count > last_positions_count)
   {
      position_just_opened = true;
      position_open_time = TimeCurrent();
   }
   else if(current_positions_count == 0)
   {
      position_just_opened = false;
   }
   
   last_positions_count = current_positions_count;
}

//+------------------------------------------------------------------+
//| 文本换行处理函数                                                  |
//+------------------------------------------------------------------+
string WrapText(string text, int max_length = 30)
{
   string result = "";
   int text_length = StringLen(text);
   
   for(int i = 0; i < text_length; i += max_length)
   {
      if(i > 0) result += "\n";
      result += StringSubstr(text, i, max_length);
   }
   
   return result;
}

//+------------------------------------------------------------------+
//| 获取心理提示文本 - 优化显示版                                    |
//+------------------------------------------------------------------+
string GetPsychologyText(TRADE_STATE state, TRADING_MODE mode)
{
   string text = "";
   
   switch(state)
   {
      case STATE_NO_POSITION:
         if(mode == MODE_DAY_SNIPER)
         {
            string texts[] = {
               "【空仓等待】\n你现在很安全！\n不要因无聊而开仓！",
               "【耐心测试】\n手又痒了？\n这就是亏钱原因！",
               "【纪律考验】\n没信号就是最好信号\n告诉你不要交易！"
            };
            text = texts[MathRand() % ArraySize(texts)];
         }
         else
         {
            string texts[] = {
               "【波段等待】\n等一个大机会！\n不是小打小闹！",
               "【趋势未现】\n现在进场是赌博！\n你是来赚钱的！",
               "【资本保护】\n空仓是最好朋友！\n比盈利更重要！"
            };
            text = texts[MathRand() % ArraySize(texts)];
         }
         break;
         
      case STATE_POSITION_JUST_OPENED:
         {
            string texts[] = {
               "【刚开仓】\n现在最危险！\n不要调整止损！",
               "【执行阶段】\n计划已定，执行！\n聪明想法是愚蠢！"
            };
            text = texts[MathRand() % ArraySize(texts)];
         }
         break;
         
      case STATE_POSITION_IN_PROFIT:
         if(mode == MODE_DAY_SNIPER)
         {
            text = "【日内盈利】\n不要高兴太早！\n坚持到目标位！";
         }
         else
         {
            string texts[] = {
               "【波段盈利】\n这才刚开始！\n敌人是贪婪！",
               "【趋势进行中】\n盈利让你飘了？\n让利润奔跑！"
            };
            text = texts[MathRand() % ArraySize(texts)];
         }
         break;
         
      case STATE_POSITION_LOSING:
         {
            string texts[] = {
               "【浮亏警告】\n想扛单？想加仓？\n这会让你破产！",
               "【止损准备】\n亏损是交易一部分！\n执行止损！",
               "【现实检查】\n市场说你错了！\n承认错误离场！"
            };
            text = texts[MathRand() % ArraySize(texts)];
         }
         break;
         
      case STATE_HEAVY_POSITION:
         text = "🚨【仓位过重】🚨\n你疯了吗？\n这不是交易！\n这是自杀！";
         break;
         
      case STATE_LOSING_STREAK:
         {
            string texts[] = {
               "🔥【连亏" + IntegerToString(consecutive_losses) + "笔】🔥\n停手！失控了！\n今天到此为止！",
               "💀【情绪失控】💀\n你是亏钱机器！\n关电脑冷静！"
            };
            text = texts[MathRand() % ArraySize(texts)];
         }
         break;
         
      case STATE_HUGE_WIN:
         {
            string texts[] = {
               "⚠️【大赚警告】⚠️\n你现在很危险！\n回归纪律！",
               "🎯【成功陷阱】🎯\n觉得自己天才？\n市场教你做人！"
            };
            text = texts[MathRand() % ArraySize(texts)];
         }
         break;
         
      case STATE_LOW_VOLATILITY:
         if(mode == MODE_DAY_SNIPER)
         {
            text = "😴【市场沉睡】😴\n现在交易烧钱！\n手续费赚不回！";
         }
         else
         {
            text = "📊【横盘整理】📊\n耐心！大行情酝酿！\n进场送钱！";
         }
         break;
         
      case STATE_APPROACHING_NEWS:
         text = "📢【新闻来袭】📢\n15分钟后重要数据！\n立即平仓！";
         break;
   }
   
   return text;
}

//+------------------------------------------------------------------+
//| 获取状态对应的颜色主题                                            |
//+------------------------------------------------------------------+
void GetStateColors(TRADE_STATE state, color &bg_color, color &text_color)
{
   switch(state)
   {
      case STATE_HEAVY_POSITION:
      case STATE_LOSING_STREAK:
         // 最危险状态 - 深红色背景，白色粗体文字
         bg_color = theme.danger_bg;
         text_color = theme.danger_text;
         break;
         
      case STATE_POSITION_LOSING:
         // 警告状态 - 橙色背景，黑色文字
         bg_color = theme.warning_bg;
         text_color = theme.warning_text;
         break;
         
      case STATE_POSITION_IN_PROFIT:
      case STATE_HUGE_WIN:
         // 盈利状态 - 深绿色背景，白色文字
         bg_color = theme.profit_bg;
         text_color = theme.profit_text;
         break;
         
      case STATE_POSITION_JUST_OPENED:
      case STATE_APPROACHING_NEWS:
         // 特殊状态 - 紫色背景，白色文字
         bg_color = theme.special_bg;
         text_color = theme.special_text;
         break;
         
      case STATE_NO_POSITION:
      case STATE_LOW_VOLATILITY:
      default:
         // 正常状态 - 深蓝色背景，白色文字
         bg_color = theme.normal_bg;
         text_color = theme.normal_text;
         break;
   }
}

//+------------------------------------------------------------------+
//| 更新心理标签 - 显示优化版                                        |
//+------------------------------------------------------------------+
void UpdatePsychologyLabel()
{
   // 检测新开仓
   CheckNewPosition();
   
   // 获取当前状态和模式
   TRADE_STATE current_state = GetTradeState();
   TRADING_MODE current_mode = GetTradingMode();
   
   // 处理闪烁效果
   HandleBlinkEffect(current_state);
   
   // 获取提示文本
   string psychology_text = GetPsychologyText(current_state, current_mode);
   
   // 获取颜色主题
   color bg_color, text_color;
   GetStateColors(current_state, bg_color, text_color);
   
   // 闪烁效果处理 - 危险状态时背景色闪烁
   if(blink_state && (current_state == STATE_HEAVY_POSITION || current_state == STATE_LOSING_STREAK))
   {
      bg_color = clrYellow;  // 闪烁时使用黄色背景
      text_color = clrRed;   // 红色文字
   }
   
   // 添加模式标识 - 简化版
   string mode_text = (current_mode == MODE_DAY_SNIPER) ? "🎯日内模式" : "🏹波段模式";
   
   // 添加简化账户信息
   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double equity = AccountInfoDouble(ACCOUNT_EQUITY);
   double profit = equity - balance;
   int positions = PositionsTotal();
   
   string account_info = StringFormat("余额:%.0f 净值:%.0f %s%.0f 持仓:%d", 
                                     balance, equity, 
                                     (profit >= 0 ? "+" : ""), MathAbs(profit),
                                     positions);
   
   // 添加时间戳
   string time_info = StringFormat("%s", TimeToString(TimeCurrent(), TIME_MINUTES));
   
   // 组合最终文本 - 简化格式，避免过长
   string final_text = mode_text + " | " + time_info + "\n" + 
                      "━━━━\n" +
                      psychology_text + "\n" +
                      "━━━━\n" +
                      account_info;
   
   // 更新标签属性
   ObjectSetString(0, label_name, OBJPROP_TEXT, final_text);
   ObjectSetInteger(0, label_name, OBJPROP_BGCOLOR, bg_color);
   ObjectSetInteger(0, label_name, OBJPROP_COLOR, text_color);
   
   // 根据状态调整字体大小 - 适当减小
   int font_size = FontSize - 1;
   if(current_state == STATE_HEAVY_POSITION || current_state == STATE_LOSING_STREAK)
   {
      font_size = FontSize + 1;  // 危险状态字体稍大
   }
   ObjectSetInteger(0, label_name, OBJPROP_FONTSIZE, font_size);
   
   // 刷新图表
   ChartRedraw();
}
