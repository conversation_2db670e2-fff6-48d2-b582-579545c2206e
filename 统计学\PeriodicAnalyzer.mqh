//+------------------------------------------------------------------+
//|                                              PeriodicAnalyzer.mqh |
//|                                  动态概率统计分析系统周期效应分析器 |
//|                                                                  |
//+------------------------------------------------------------------+

#ifndef PERIODIC_ANALYZER_MQH
#define PERIODIC_ANALYZER_MQH

#include "Defines.mqh"
#include "DataCollector.mqh"

//+------------------------------------------------------------------+
//| 结果缓存区                                                       |
//+------------------------------------------------------------------+
static StatisticalResult g_weekday_stats[7];  // 用于存储周日(0)到周六(6)的统计结果
static StatisticalResult g_month_stats[13];   // 索引0不用，用索引1-12存储1月到12月的结果

//+------------------------------------------------------------------+
//| 初始化统计结果                                                   |
//+------------------------------------------------------------------+
void InitializeStatisticalResult(StatisticalResult &result)
  {
   result.total_samples = 0;
   result.win_samples = 0;
   result.win_rate = 0.0;
   result.total_volatility_pips = 0.0;
   result.avg_volatility_pips = 0.0;
  }

//+------------------------------------------------------------------+
//| 计算周期性统计数据                                               |
//+------------------------------------------------------------------+
void Analyzer_CalculatePeriodicStats()
  {
   Print("开始计算周期性统计数据...");
   
   // 初始化所有统计结果
   for(int i = 0; i < 7; i++)
     {
      InitializeStatisticalResult(g_weekday_stats[i]);
     }
   
   for(int i = 0; i < 13; i++)
     {
      InitializeStatisticalResult(g_month_stats[i]);
     }
   
   // 获取数据缓存大小
   int cache_size = Data_GetCacheSize();
   if(cache_size <= 0)
     {
      Print("没有可用的历史数据进行周期性分析");
      return;
     }
   
   Print("开始分析 ", cache_size, " 天的历史数据...");
   
   // 遍历所有历史数据
   for(int i = 0; i < cache_size; i++)
     {
      HistoricalDailyData data = Data_GetDailyDataAt(i);
      
      if(data.date_start_of_day == 0)
         continue;
      
      // 判断是否为上涨日
      bool is_up_day = (data.close > data.open);
      
      // 计算波幅（以点数为单位）
      double range_pips = (data.high - data.low) / Point();
      
      // 获取星期几 (MQL5中: 0=周日, 1=周一, ..., 6=周六)
      int day_of_week = data.day_of_week;
      if(day_of_week >= 0 && day_of_week <= 6)
        {
         g_weekday_stats[day_of_week].total_samples++;
         if(is_up_day)
            g_weekday_stats[day_of_week].win_samples++;
         g_weekday_stats[day_of_week].total_volatility_pips += range_pips;
        }
      
      // 获取月份 (1-12)
      int month = data.month_of_year;
      if(month >= 1 && month <= 12)
        {
         g_month_stats[month].total_samples++;
         if(is_up_day)
            g_month_stats[month].win_samples++;
         g_month_stats[month].total_volatility_pips += range_pips;
        }
     }
   
   // 计算最终的胜率和平均波幅
   for(int i = 0; i < 7; i++)
     {
      if(g_weekday_stats[i].total_samples > 0)
        {
         g_weekday_stats[i].win_rate = (double)g_weekday_stats[i].win_samples / g_weekday_stats[i].total_samples;
         g_weekday_stats[i].avg_volatility_pips = g_weekday_stats[i].total_volatility_pips / g_weekday_stats[i].total_samples;
        }
     }
   
   for(int i = 1; i <= 12; i++)
     {
      if(g_month_stats[i].total_samples > 0)
        {
         g_month_stats[i].win_rate = (double)g_month_stats[i].win_samples / g_month_stats[i].total_samples;
         g_month_stats[i].avg_volatility_pips = g_month_stats[i].total_volatility_pips / g_month_stats[i].total_samples;
        }
     }
   
   Print("周期性统计数据计算完成");
   
   // 输出调试信息
   for(int i = 0; i < 7; i++)
     {
      if(g_weekday_stats[i].total_samples > 0)
        {
         string day_name = "";
         switch(i)
           {
            case 0: day_name = "周日"; break;
            case 1: day_name = "周一"; break;
            case 2: day_name = "周二"; break;
            case 3: day_name = "周三"; break;
            case 4: day_name = "周四"; break;
            case 5: day_name = "周五"; break;
            case 6: day_name = "周六"; break;
           }
         Print(day_name, ": 样本数=", g_weekday_stats[i].total_samples, 
               ", 胜率=", StringFormat("%.1f%%", g_weekday_stats[i].win_rate * 100),
               ", 平均波幅=", StringFormat("%.1f", g_weekday_stats[i].avg_volatility_pips), "点");
        }
     }
  }

//+------------------------------------------------------------------+
//| 获取星期统计数据                                                 |
//+------------------------------------------------------------------+
StatisticalResult Analyzer_GetWeekdayStats(int day_of_week)
  {
   StatisticalResult empty_result;
   InitializeStatisticalResult(empty_result);
   
   if(day_of_week >= 0 && day_of_week <= 6)
     {
      return g_weekday_stats[day_of_week];
     }
   
   return empty_result;
  }

//+------------------------------------------------------------------+
//| 获取月份统计数据                                                 |
//+------------------------------------------------------------------+
StatisticalResult Analyzer_GetMonthStats(int month)
  {
   StatisticalResult empty_result;
   InitializeStatisticalResult(empty_result);
   
   if(month >= 1 && month <= 12)
     {
      return g_month_stats[month];
     }
   
   return empty_result;
  }

//+------------------------------------------------------------------+
//| 获取星期名称                                                     |
//+------------------------------------------------------------------+
string Analyzer_GetWeekdayName(int day_of_week)
  {
   switch(day_of_week)
     {
      case 0: return "周日";
      case 1: return "周一";
      case 2: return "周二";
      case 3: return "周三";
      case 4: return "周四";
      case 5: return "周五";
      case 6: return "周六";
      default: return "未知";
     }
  }

//+------------------------------------------------------------------+
//| 获取月份名称                                                     |
//+------------------------------------------------------------------+
string Analyzer_GetMonthName(int month)
  {
   switch(month)
     {
      case 1: return "1月";
      case 2: return "2月";
      case 3: return "3月";
      case 4: return "4月";
      case 5: return "5月";
      case 6: return "6月";
      case 7: return "7月";
      case 8: return "8月";
      case 9: return "9月";
      case 10: return "10月";
      case 11: return "11月";
      case 12: return "12月";
      default: return "未知";
     }
  }

#endif // PERIODIC_ANALYZER_MQH
